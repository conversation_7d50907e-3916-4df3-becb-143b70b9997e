import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import identityGenerator from '../services/identityGenerator';
import { GenerationOptions, ApiResponse } from '../types';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { generationRateLimiter } from '../middleware/security';

const router = Router();

// Validation middleware
const validateGenerationOptions = [
  body('country')
    .isString()
    .isLength({ min: 2, max: 3 })
    .withMessage('Country code must be 2-3 characters'),
  body('gender')
    .optional()
    .isIn(['male', 'female', 'random'])
    .withMessage('Gender must be male, female, or random'),
  body('ageRange.min')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Minimum age must be between 1 and 100'),
  body('ageRange.max')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Maximum age must be between 1 and 100'),
  body('includeSSN')
    .optional()
    .isBoolean()
    .withMessage('includeSSN must be a boolean'),
  body('includeCreditCard')
    .optional()
    .isBoolean()
    .withMessage('includeCreditCard must be a boolean'),
  body('quantity')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Quantity must be between 1 and 100'),
];

// GET /api/identity/countries - Get supported countries
router.get('/countries', asyncHandler(async (req: Request, res: Response) => {
  const countries = identityGenerator.getSupportedCountries();
  
  const response: ApiResponse = {
    success: true,
    data: countries,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// POST /api/identity/generate - Generate single identity
router.post('/generate', 
  generationRateLimiter,
  validateGenerationOptions,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400);
    }

    const options: GenerationOptions = {
      country: req.body.country || 'US',
      gender: req.body.gender || 'random',
      ageRange: req.body.ageRange || { min: 18, max: 65 },
      includeSSN: req.body.includeSSN !== false,
      includeCreditCard: req.body.includeCreditCard !== false,
      quantity: 1,
    };

    // Validate age range
    if (options.ageRange && options.ageRange.min > options.ageRange.max) {
      throw new AppError('Minimum age cannot be greater than maximum age', 400);
    }

    const identity = identityGenerator.generateIdentity(options);
    
    const response: ApiResponse = {
      success: true,
      data: identity,
      message: 'Identity generated successfully',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// POST /api/identity/generate-batch - Generate multiple identities
router.post('/generate-batch',
  generationRateLimiter,
  validateGenerationOptions,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400);
    }

    const options: GenerationOptions = {
      country: req.body.country || 'US',
      gender: req.body.gender || 'random',
      ageRange: req.body.ageRange || { min: 18, max: 65 },
      includeSSN: req.body.includeSSN !== false,
      includeCreditCard: req.body.includeCreditCard !== false,
      quantity: Math.min(req.body.quantity || 1, 100), // Limit to 100
    };

    // Validate age range
    if (options.ageRange && options.ageRange.min > options.ageRange.max) {
      throw new AppError('Minimum age cannot be greater than maximum age', 400);
    }

    const identities = identityGenerator.generateMultipleIdentities(options);
    
    const response: ApiResponse = {
      success: true,
      data: identities,
      message: `${identities.length} identities generated successfully`,
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// GET /api/identity/formats/:country - Get format information for a country
router.get('/formats/:country', asyncHandler(async (req: Request, res: Response) => {
  const { country } = req.params;
  const countries = identityGenerator.getSupportedCountries();
  const countryInfo = countries.find(c => c.code === country.toUpperCase());

  if (!countryInfo) {
    throw new AppError('Country not supported', 404);
  }

  const response: ApiResponse = {
    success: true,
    data: countryInfo,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;

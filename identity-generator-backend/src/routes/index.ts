import { Router } from 'express';
import identityRoutes from './identity';
import healthRoutes from './health';

const router = Router();

// Mount routes
router.use('/identity', identityRoutes);
router.use('/health', healthRoutes);

// API info endpoint
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'Identity Generator API',
      version: '1.0.0',
      description: 'Virtual Identity Information Generator API',
      endpoints: {
        identity: '/api/identity',
        health: '/api/health',
      },
      documentation: '/api/docs',
    },
    timestamp: new Date().toISOString(),
  });
});

export default router;

export interface IdentityData {
  id: string;
  country: string;
  firstName: string;
  lastName: string;
  fullName: string;
  gender: 'male' | 'female';
  age: number;
  birthDate: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  ssn?: string;
  creditCard?: {
    number: string;
    type: string;
    expiryDate: string;
    cvv: string;
  };
  occupation: string;
  company: string;
  height: string;
  weight: string;
  bloodType: string;
  createdAt: string;
}

export interface GenerationOptions {
  country: string;
  gender?: 'male' | 'female' | 'random';
  ageRange?: {
    min: number;
    max: number;
  };
  includeSSN: boolean;
  includeCreditCard: boolean;
  quantity: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  plan: 'free' | 'personal' | 'professional' | 'enterprise';
  subscription: {
    isActive: boolean;
    expiresAt?: string;
    generationsLeft: number;
    generationsLimit: number;
  };
  createdAt: string;
  lastLoginAt: string;
}

export interface Country {
  code: string;
  name: string;
  flag: string;
  formats: {
    phone: string;
    postalCode: string;
    ssn?: string;
  };
}

export interface RequestWithUser extends Request {
  user?: User;
}

export type ExportFormat = 'json' | 'csv' | 'xml' | 'pdf' | 'txt';

import { IdentityData, GenerationOptions, Country } from '../types';
import { v4 as uuidv4 } from 'uuid';

// Sample data for generation
const COUNTRIES: Record<string, Country> = {
  US: {
    code: 'US',
    name: 'United States',
    flag: '🇺🇸',
    formats: {
      phone: '+1 (XXX) XXX-XXXX',
      postalCode: 'XXXXX',
      ssn: 'XXX-XX-XXXX',
    },
  },
  UK: {
    code: 'UK',
    name: 'United Kingdom',
    flag: '🇬🇧',
    formats: {
      phone: '+44 XXXX XXXXXX',
      postalCode: 'XX XX XXX',
    },
  },
  CA: {
    code: 'CA',
    name: 'Canada',
    flag: '🇨🇦',
    formats: {
      phone: '+1 (XXX) XXX-XXXX',
      postalCode: 'XXX XXX',
      ssn: 'XXX XXX XXX',
    },
  },
  // Add more countries as needed
};

const FIRST_NAMES = {
  male: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
  female: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
};

const LAST_NAMES = ['<PERSON>', '<PERSON>', 'Williams', '<PERSON>', 'Jones', 'Garcia', '<PERSON>', 'Davis', 'Rodriguez', 'Martinez'];

const OCCUPATIONS = [
  'Software Engineer', 'Teacher', 'Doctor', 'Nurse', 'Manager', 'Sales Representative',
  'Accountant', 'Designer', 'Writer', 'Consultant', 'Analyst', 'Developer',
];

const COMPANIES = [
  'Tech Solutions Inc', 'Global Dynamics', 'Innovation Corp', 'Future Systems',
  'Digital Ventures', 'Smart Technologies', 'Advanced Solutions', 'Creative Industries',
];

const BLOOD_TYPES = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

const CREDIT_CARD_TYPES = ['Visa', 'MasterCard', 'American Express', 'Discover'];

class IdentityGeneratorService {
  private getRandomElement<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private generateRandomNumber(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  private generateRandomString(length: number, chars: string = '**********'): string {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private generatePhone(country: string): string {
    const format = COUNTRIES[country]?.formats.phone || '+1 (XXX) XXX-XXXX';
    return format.replace(/X/g, () => this.generateRandomString(1));
  }

  private generateSSN(country: string): string | undefined {
    const format = COUNTRIES[country]?.formats.ssn;
    if (!format) return undefined;
    return format.replace(/X/g, () => this.generateRandomString(1));
  }

  private generateCreditCard(): {
    number: string;
    type: string;
    expiryDate: string;
    cvv: string;
  } {
    const type = this.getRandomElement(CREDIT_CARD_TYPES);
    let prefix = '4'; // Default to Visa
    
    switch (type) {
      case 'MasterCard':
        prefix = '5';
        break;
      case 'American Express':
        prefix = '3';
        break;
      case 'Discover':
        prefix = '6';
        break;
    }

    const number = prefix + this.generateRandomString(15);
    const month = this.generateRandomNumber(1, 12).toString().padStart(2, '0');
    const year = this.generateRandomNumber(2025, 2030);
    const expiryDate = `${month}/${year}`;
    const cvv = this.generateRandomString(3);

    return { number, type, expiryDate, cvv };
  }

  private generateAddress(country: string): {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  } {
    const streetNumber = this.generateRandomNumber(1, 9999);
    const streetNames = ['Main St', 'Oak Ave', 'Park Rd', 'First St', 'Second Ave', 'Elm St'];
    const street = `${streetNumber} ${this.getRandomElement(streetNames)}`;

    const cities = {
      US: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
      UK: ['London', 'Manchester', 'Birmingham', 'Liverpool', 'Leeds'],
      CA: ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa'],
    };

    const states = {
      US: ['NY', 'CA', 'TX', 'FL', 'IL'],
      UK: ['England', 'Scotland', 'Wales', 'Northern Ireland'],
      CA: ['ON', 'BC', 'QC', 'AB', 'MB'],
    };

    const city = this.getRandomElement(cities[country as keyof typeof cities] || cities.US);
    const state = this.getRandomElement(states[country as keyof typeof states] || states.US);
    
    const zipFormat = COUNTRIES[country]?.formats.postalCode || 'XXXXX';
    const zipCode = zipFormat.replace(/X/g, () => this.generateRandomString(1));

    return {
      street,
      city,
      state,
      zipCode,
      country: COUNTRIES[country]?.name || 'United States',
    };
  }

  private generateBirthDate(age: number): string {
    const today = new Date();
    const birthYear = today.getFullYear() - age;
    const birthMonth = this.generateRandomNumber(1, 12);
    const birthDay = this.generateRandomNumber(1, 28); // Safe day range
    
    return new Date(birthYear, birthMonth - 1, birthDay).toISOString().split('T')[0];
  }

  private generateEmail(firstName: string, lastName: string): string {
    const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
    const domain = this.getRandomElement(domains);
    const randomNum = this.generateRandomNumber(1, 999);
    
    return `${firstName.toLowerCase()}.${lastName.toLowerCase()}${randomNum}@${domain}`;
  }

  public generateIdentity(options: GenerationOptions): IdentityData {
    const gender = options.gender === 'random' 
      ? this.getRandomElement(['male', 'female'] as const)
      : options.gender || 'male';

    const firstName = this.getRandomElement(FIRST_NAMES[gender]);
    const lastName = this.getRandomElement(LAST_NAMES);
    const fullName = `${firstName} ${lastName}`;

    const ageMin = options.ageRange?.min || 18;
    const ageMax = options.ageRange?.max || 65;
    const age = this.generateRandomNumber(ageMin, ageMax);

    const address = this.generateAddress(options.country);
    const email = this.generateEmail(firstName, lastName);
    const phone = this.generatePhone(options.country);
    const birthDate = this.generateBirthDate(age);

    const identity: IdentityData = {
      id: uuidv4(),
      country: options.country,
      firstName,
      lastName,
      fullName,
      gender,
      age,
      birthDate,
      email,
      phone,
      address,
      occupation: this.getRandomElement(OCCUPATIONS),
      company: this.getRandomElement(COMPANIES),
      height: `${this.generateRandomNumber(150, 200)} cm`,
      weight: `${this.generateRandomNumber(50, 120)} kg`,
      bloodType: this.getRandomElement(BLOOD_TYPES),
      createdAt: new Date().toISOString(),
    };

    if (options.includeSSN) {
      identity.ssn = this.generateSSN(options.country);
    }

    if (options.includeCreditCard) {
      identity.creditCard = this.generateCreditCard();
    }

    return identity;
  }

  public generateMultipleIdentities(options: GenerationOptions): IdentityData[] {
    const identities: IdentityData[] = [];
    
    for (let i = 0; i < options.quantity; i++) {
      identities.push(this.generateIdentity(options));
    }

    return identities;
  }

  public getSupportedCountries(): Country[] {
    return Object.values(COUNTRIES);
  }
}

export default new IdentityGeneratorService();

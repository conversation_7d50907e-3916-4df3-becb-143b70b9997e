import { <PERSON>, Button, Typography, Row, Col, Divider, Space, Tag } from 'antd';
import { CopyOutlined, DownloadOutlined, UserOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { IdentityData } from '../../types';
import { useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';

const { Text, Title } = Typography;

interface IdentityCardProps {
  identity: IdentityData;
  showActions?: boolean;
  compact?: boolean;
}

const IdentityCard: React.FC<IdentityCardProps> = ({
  identity,
  showActions = true,
  compact = false,
}) => {
  const dispatch = useAppDispatch();

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    dispatch(addNotification({
      type: 'success',
      title: 'Copied',
      message: `${label} copied to clipboard`,
    }));
  };

  const downloadAsJson = () => {
    const dataStr = JSON.stringify(identity, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `identity-${identity.id}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const renderField = (label: string, value: string | undefined, copyable: boolean = true) => {
    if (!value) return null;
    
    return (
      <div className={`${compact ? 'mb-2' : 'mb-3'} p-2 bg-gray-50 rounded-lg`}>
        <div className="flex justify-between items-center">
          <Text strong className="text-gray-600 text-xs">{label}</Text>
          {copyable && (
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => copyToClipboard(value, label)}
              className="text-gray-500 hover:text-primary-500 h-6 w-6 p-0"
            />
          )}
        </div>
        <Text className="text-gray-900 font-mono text-sm">{value}</Text>
      </div>
    );
  };

  const getGenderColor = (gender: string) => {
    return gender === 'male' ? 'blue' : gender === 'female' ? 'pink' : 'default';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        className="card-shadow hover:card-shadow-lg transition-all duration-300"
        title={
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <UserOutlined className="text-primary-500" />
              <span>{identity.fullName}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Tag color={getGenderColor(identity.gender)}>
                {identity.gender.toUpperCase()}
              </Tag>
              <Tag>{identity.age} years</Tag>
              <Tag>{identity.country}</Tag>
            </div>
          </div>
        }
        extra={
          showActions && (
            <Space>
              <Button
                icon={<DownloadOutlined />}
                onClick={downloadAsJson}
                size="small"
              >
                Export
              </Button>
            </Space>
          )
        }
      >
        <Row gutter={[16, 16]}>
          {!compact && (
            <Col xs={24} md={8}>
              <Title level={5}>Personal Information</Title>
              {renderField('Full Name', identity.fullName)}
              {renderField('First Name', identity.firstName)}
              {renderField('Last Name', identity.lastName)}
              {renderField('Birth Date', identity.birthDate)}
              {renderField('Blood Type', identity.bloodType)}
              {renderField('Height', identity.height)}
              {renderField('Weight', identity.weight)}
            </Col>
          )}
          
          <Col xs={24} md={compact ? 12 : 8}>
            <Title level={5}>Contact Information</Title>
            {renderField('Email', identity.email)}
            {renderField('Phone', identity.phone)}
            
            {!compact && <Divider />}
            
            <Title level={5}>Address</Title>
            {renderField('Street', identity.address.street)}
            {renderField('City', identity.address.city)}
            {renderField('State', identity.address.state)}
            {renderField('ZIP Code', identity.address.zipCode)}
            {renderField('Country', identity.address.country)}
          </Col>
          
          <Col xs={24} md={compact ? 12 : 8}>
            <Title level={5}>Professional</Title>
            {renderField('Occupation', identity.occupation)}
            {renderField('Company', identity.company)}
            
            {identity.ssn && (
              <>
                {!compact && <Divider />}
                <Title level={5}>SSN</Title>
                {renderField('SSN', identity.ssn)}
              </>
            )}
            
            {identity.creditCard && (
              <>
                {!compact && <Divider />}
                <Title level={5}>Credit Card</Title>
                {renderField('Number', identity.creditCard.number)}
                {renderField('Type', identity.creditCard.type)}
                {renderField('Expiry', identity.creditCard.expiryDate)}
                {renderField('CVV', identity.creditCard.cvv)}
              </>
            )}
          </Col>
        </Row>
        
        {!compact && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Text className="text-xs text-gray-500">
              Generated on: {new Date(identity.createdAt).toLocaleString()}
            </Text>
          </div>
        )}
      </Card>
    </motion.div>
  );
};

export default IdentityCard;

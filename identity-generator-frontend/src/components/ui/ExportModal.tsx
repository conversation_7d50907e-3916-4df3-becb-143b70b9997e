import { useState } from 'react';
import { Modal, Form, Select, Checkbox, Button, Typography, Space, Divider } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { IdentityData, ExportFormat } from '../../types';
import { EXPORT_FORMATS } from '../../constants';

const { Text } = Typography;
const { Option } = Select;

interface ExportModalProps {
  visible: boolean;
  onCancel: () => void;
  identities: IdentityData[];
  title?: string;
}

const ExportModal: React.FC<ExportModalProps> = ({
  visible,
  onCancel,
  identities,
  title = 'Export Identities',
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const allFields = [
    { key: 'fullName', label: 'Full Name' },
    { key: 'firstName', label: 'First Name' },
    { key: 'lastName', label: 'Last Name' },
    { key: 'gender', label: 'Gender' },
    { key: 'age', label: 'Age' },
    { key: 'birthDate', label: 'Birth Date' },
    { key: 'email', label: 'Email' },
    { key: 'phone', label: 'Phone' },
    { key: 'address.street', label: 'Street Address' },
    { key: 'address.city', label: 'City' },
    { key: 'address.state', label: 'State' },
    { key: 'address.zipCode', label: 'ZIP Code' },
    { key: 'address.country', label: 'Country' },
    { key: 'occupation', label: 'Occupation' },
    { key: 'company', label: 'Company' },
    { key: 'height', label: 'Height' },
    { key: 'weight', label: 'Weight' },
    { key: 'bloodType', label: 'Blood Type' },
    { key: 'ssn', label: 'SSN' },
    { key: 'creditCard.number', label: 'Credit Card Number' },
    { key: 'creditCard.type', label: 'Credit Card Type' },
    { key: 'creditCard.expiryDate', label: 'Credit Card Expiry' },
    { key: 'creditCard.cvv', label: 'Credit Card CVV' },
  ];

  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  const exportAsJSON = (data: any[], filename: string) => {
    const jsonStr = JSON.stringify(data, null, 2);
    downloadFile(jsonStr, filename, 'application/json');
  };

  const exportAsCSV = (data: any[], fields: string[], filename: string) => {
    const headers = fields.map(field => 
      allFields.find(f => f.key === field)?.label || field
    );
    
    const csvContent = [
      headers.join(','),
      ...data.map(item => 
        fields.map(field => {
          const value = getNestedValue(item, field);
          return typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value || '';
        }).join(',')
      )
    ].join('\n');
    
    downloadFile(csvContent, filename, 'text/csv');
  };

  const exportAsXML = (data: any[], filename: string) => {
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<identities>
${data.map(identity => `  <identity>
${Object.entries(identity).map(([key, value]) => {
  if (typeof value === 'object' && value !== null) {
    return Object.entries(value).map(([subKey, subValue]) => 
      `    <${key}_${subKey}>${subValue}</${key}_${subKey}>`
    ).join('\n');
  }
  return `    <${key}>${value}</${key}>`;
}).join('\n')}
  </identity>`).join('\n')}
</identities>`;
    
    downloadFile(xmlContent, filename, 'application/xml');
  };

  const exportAsText = (data: any[], fields: string[], filename: string) => {
    const textContent = data.map((identity, index) => {
      const lines = [`Identity ${index + 1}:`];
      fields.forEach(field => {
        const label = allFields.find(f => f.key === field)?.label || field;
        const value = getNestedValue(identity, field);
        if (value) {
          lines.push(`${label}: ${value}`);
        }
      });
      return lines.join('\n');
    }).join('\n\n');
    
    downloadFile(textContent, filename, 'text/plain');
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleExport = async (values: any) => {
    setLoading(true);
    try {
      const { format, fields, includeHeaders } = values;
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `identities-${timestamp}.${format}`;

      const exportData = identities.map(identity => {
        const filtered: any = {};
        fields.forEach((field: string) => {
          const value = getNestedValue(identity, field);
          if (value !== undefined) {
            if (field.includes('.')) {
              const [parent, child] = field.split('.');
              if (!filtered[parent]) filtered[parent] = {};
              filtered[parent][child] = value;
            } else {
              filtered[field] = value;
            }
          }
        });
        return filtered;
      });

      switch (format) {
        case 'json':
          exportAsJSON(exportData, filename);
          break;
        case 'csv':
          exportAsCSV(identities, fields, filename);
          break;
        case 'xml':
          exportAsXML(exportData, filename);
          break;
        case 'txt':
          exportAsText(identities, fields, filename);
          break;
        default:
          throw new Error('Unsupported format');
      }

      onCancel();
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleExport}
        initialValues={{
          format: 'json',
          fields: ['fullName', 'email', 'phone', 'address.street', 'address.city'],
          includeHeaders: true,
        }}
      >
        <Form.Item
          name="format"
          label="Export Format"
          rules={[{ required: true, message: 'Please select a format' }]}
        >
          <Select placeholder="Select export format">
            {EXPORT_FORMATS.map(format => (
              <Option key={format.value} value={format.value}>
                <Space>
                  <span>{format.icon}</span>
                  <span>{format.label}</span>
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="fields"
          label="Fields to Export"
          rules={[{ required: true, message: 'Please select at least one field' }]}
        >
          <Checkbox.Group className="w-full">
            <div className="grid grid-cols-2 gap-2">
              {allFields.map(field => (
                <Checkbox key={field.key} value={field.key}>
                  {field.label}
                </Checkbox>
              ))}
            </div>
          </Checkbox.Group>
        </Form.Item>

        <Form.Item name="includeHeaders" valuePropName="checked">
          <Checkbox>Include headers (for CSV format)</Checkbox>
        </Form.Item>

        <Divider />

        <div className="text-center">
          <Text className="text-gray-600 mb-4 block">
            Exporting {identities.length} identit{identities.length === 1 ? 'y' : 'ies'}
          </Text>
          
          <Space>
            <Button onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              icon={<DownloadOutlined />}
              loading={loading}
            >
              Export
            </Button>
          </Space>
        </div>
      </Form>
    </Modal>
  );
};

export default ExportModal;

import { useEffect } from 'react';
import { notification } from 'antd';
import { useAppSelector, useAppDispatch } from '../../store';
import { removeNotification } from '../../store/slices/uiSlice';

const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { notifications } = useAppSelector((state) => state.ui);

  useEffect(() => {
    notifications.forEach((notif) => {
      notification[notif.type]({
        message: notif.title,
        description: notif.message,
        duration: notif.duration || 4.5,
        onClose: () => {
          dispatch(removeNotification(notif.id));
        },
      });
    });
  }, [notifications, dispatch]);

  return <>{children}</>;
};

export default NotificationProvider;

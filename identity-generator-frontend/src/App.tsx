import { Provider } from 'react-redux';
import { RouterProvider } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { store } from './store';
import router from './router';
import { APP_NAME } from './constants';
import NotificationProvider from './components/ui/NotificationProvider';
import './App.css';

// Ant Design theme configuration
const antdTheme = {
  token: {
    colorPrimary: '#3b82f6',
    colorSuccess: '#10b981',
    colorWarning: '#f59e0b',
    colorError: '#ef4444',
    borderRadius: 8,
    fontFamily: 'Inter, system-ui, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Select: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Card: {
      borderRadius: 12,
    },
  },
};

function App() {
  return (
    <Provider store={store}>
      <ConfigProvider theme={antdTheme}>
        <NotificationProvider>
          <div className="App">
            <RouterProvider router={router} />
          </div>
        </NotificationProvider>
      </ConfigProvider>
    </Provider>
  );
}

export default App;

import { useState } from 'react';
import { Typography, Row, Col, Input, Select, Button, Empty, Space } from 'antd';
import { SearchOutlined, DeleteOutlined, ClearOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useAppSelector, useAppDispatch } from '../store';
import { removeFromHistory, clearHistory } from '../store/slices/identitySlice';
import IdentityCard from '../components/ui/IdentityCard';
import { COUNTRIES } from '../constants';

const { Title, Text } = Typography;
const { Option } = Select;

const HistoryPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { history } = useAppSelector((state) => state.identity);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCountry, setFilterCountry] = useState<string>('');
  const [filterGender, setFilterGender] = useState<string>('');

  const filteredHistory = history.filter((identity) => {
    const matchesSearch = !searchTerm ||
      identity.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      identity.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      identity.phone.includes(searchTerm);

    const matchesCountry = !filterCountry || identity.country === filterCountry;
    const matchesGender = !filterGender || identity.gender === filterGender;

    return matchesSearch && matchesCountry && matchesGender;
  });

  const handleClearHistory = () => {
    dispatch(clearHistory());
  };

  const handleRemoveIdentity = (id: string) => {
    dispatch(removeFromHistory(id));
  };

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex justify-between items-center">
          <div>
            <Title level={2} className="mb-2">Generation History</Title>
            <Text className="text-gray-600">
              View and manage your previously generated identities
            </Text>
          </div>
          {history.length > 0 && (
            <Button
              danger
              icon={<ClearOutlined />}
              onClick={handleClearHistory}
            >
              Clear All History
            </Button>
          )}
        </div>
      </motion.div>

      {history.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Empty
            description={
              <div className="space-y-2">
                <Text className="text-gray-500">No generation history found</Text>
                <Text className="text-gray-400 text-sm">
                  Generated identities will appear here for easy access
                </Text>
              </div>
            }
            className="py-20"
          />
        </motion.div>
      ) : (
        <>
          {/* Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white p-4 rounded-lg card-shadow"
          >
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={8}>
                <Input
                  placeholder="Search by name, email, or phone"
                  prefix={<SearchOutlined />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  allowClear
                />
              </Col>
              <Col xs={12} sm={4}>
                <Select
                  placeholder="Country"
                  value={filterCountry}
                  onChange={setFilterCountry}
                  allowClear
                  className="w-full"
                >
                  {COUNTRIES.map((country) => (
                    <Option key={country.code} value={country.code}>
                      {country.flag} {country.name}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col xs={12} sm={4}>
                <Select
                  placeholder="Gender"
                  value={filterGender}
                  onChange={setFilterGender}
                  allowClear
                  className="w-full"
                >
                  <Option value="male">Male</Option>
                  <Option value="female">Female</Option>
                </Select>
              </Col>
              <Col xs={24} sm={8}>
                <Space>
                  <Text className="text-gray-600">
                    Showing {filteredHistory.length} of {history.length} identities
                  </Text>
                </Space>
              </Col>
            </Row>
          </motion.div>

          {/* History List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {filteredHistory.length === 0 ? (
              <Empty
                description="No identities match your search criteria"
                className="py-12"
              />
            ) : (
              <Row gutter={[16, 16]}>
                {filteredHistory.map((identity, index) => (
                  <Col xs={24} lg={12} key={identity.id}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <div className="relative">
                        <IdentityCard identity={identity} compact />
                        <Button
                          danger
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={() => handleRemoveIdentity(identity.id)}
                          className="absolute top-2 right-2 opacity-0 hover:opacity-100 transition-opacity"
                          title="Remove from history"
                        />
                      </div>
                    </motion.div>
                  </Col>
                ))}
              </Row>
            )}
          </motion.div>
        </>
      )}
    </div>
  );
};

export default HistoryPage;

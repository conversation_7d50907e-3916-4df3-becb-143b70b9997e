import { useState, useEffect } from 'react';
import { Row, Col, Button, Typography, Space } from 'antd';
import { ReloadOutlined, ThunderboltOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useIdentityGenerator } from '../hooks/useIdentityGenerator';
import { useAppSelector, useAppDispatch } from '../store';
import { setGenerationOptions } from '../store/slices/identitySlice';
import GenerationOptionsForm from '../components/forms/GenerationOptionsForm';
import IdentityCard from '../components/ui/IdentityCard';

const { Title, Text } = Typography;

const GeneratorPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { generationOptions, currentIdentity, isGenerating, generatedIdentities } = useAppSelector((state) => state.identity);
  const { generateSingleIdentity, generateMultipleIdentities, testConnection, loadSupportedCountries } = useIdentityGenerator();

  useEffect(() => {
    loadSupportedCountries();
    testConnection();
  }, [loadSupportedCountries, testConnection]);

  const handleGenerate = async () => {
    try {
      if (generationOptions.quantity > 1) {
        await generateMultipleIdentities();
      } else {
        await generateSingleIdentity();
      }
    } catch (error) {
      console.error('Generation failed:', error);
    }
  };

  const handleOptionsChange = (options: Partial<typeof generationOptions>) => {
    dispatch(setGenerationOptions(options));
  };

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center"
      >
        <Title level={2} className="mb-2">Virtual Identity Generator</Title>
        <Text className="text-gray-600">
          Generate realistic virtual identity information for testing and development purposes
        </Text>
      </motion.div>

      <Row gutter={[24, 24]}>
        {/* Configuration Panel */}
        <Col xs={24} lg={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <GenerationOptionsForm
              options={generationOptions}
              onChange={handleOptionsChange}
              disabled={isGenerating}
            />

            <div className="mt-4">
              <Button
                type="primary"
                size="large"
                icon={generationOptions.quantity > 1 ? <ThunderboltOutlined /> : <ReloadOutlined />}
                onClick={handleGenerate}
                loading={isGenerating}
                className="w-full"
              >
                {generationOptions.quantity > 1
                  ? `Generate ${generationOptions.quantity} Identities`
                  : 'Generate Identity'
                }
              </Button>
            </div>
          </motion.div>
        </Col>

        {/* Results Panel */}
        <Col xs={24} lg={16}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {!currentIdentity && generatedIdentities.length === 0 ? (
              <div className="text-center py-20 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                <div className="space-y-4">
                  <div className="text-6xl">🎭</div>
                  <Title level={3} className="text-gray-500">No Identity Generated Yet</Title>
                  <Text className="text-gray-400">
                    Configure your options and click "Generate Identity" to create a virtual identity
                  </Text>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Single Identity Display */}
                {currentIdentity && generationOptions.quantity === 1 && (
                  <IdentityCard identity={currentIdentity} />
                )}

                {/* Multiple Identities Display */}
                {generatedIdentities.length > 1 && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Title level={3}>Generated Identities ({generatedIdentities.length})</Title>
                      <Button
                        icon={<DownloadOutlined />}
                        onClick={() => {
                          const dataStr = JSON.stringify(generatedIdentities, null, 2);
                          const dataBlob = new Blob([dataStr], { type: 'application/json' });
                          const url = URL.createObjectURL(dataBlob);
                          const link = document.createElement('a');
                          link.href = url;
                          link.download = `identities-batch-${Date.now()}.json`;
                          link.click();
                          URL.revokeObjectURL(url);
                        }}
                      >
                        Export All
                      </Button>
                    </div>

                    <Row gutter={[16, 16]}>
                      {generatedIdentities.map((identity, index) => (
                        <Col xs={24} lg={12} key={identity.id}>
                          <IdentityCard identity={identity} compact />
                        </Col>
                      ))}
                    </Row>
                  </div>
                )}
              </div>
            )}
          </motion.div>
        </Col>
      </Row>
    </div>
  );
};

export default GeneratorPage;

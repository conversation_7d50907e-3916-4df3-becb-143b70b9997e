import { useState } from 'react';
import { Form, Input, Button, Card, Typography, Divider, Space, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, GoogleOutlined, GithubOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAppDispatch } from '../store';
import { setUser } from '../store/slices/userSlice';
import { addNotification } from '../store/slices/uiSlice';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
  remember: boolean;
}

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleLogin = async (values: LoginFormData) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock user data
      const mockUser = {
        id: '1',
        email: values.email,
        username: values.email.split('@')[0],
        firstName: 'Demo',
        lastName: 'User',
        plan: 'free' as const,
        subscription: {
          isActive: true,
          generationsLeft: 5,
          generationsLimit: 5,
        },
        preferences: {
          defaultCountry: 'US',
          defaultGender: 'random' as const,
          theme: 'light' as const,
          language: 'en',
        },
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      };

      dispatch(setUser(mockUser));
      dispatch(addNotification({
        type: 'success',
        title: 'Welcome back!',
        message: 'You have successfully logged in.',
      }));

      navigate('/');
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Login Failed',
        message: 'Invalid email or password. Please try again.',
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md"
      >
        <Card className="card-shadow-lg">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <UserOutlined className="text-white text-2xl" />
            </div>
            <Title level={2} className="mb-2">Welcome Back</Title>
            <Text className="text-gray-600">
              Sign in to your Identity Generator account
            </Text>
          </div>

          <Form
            name="login"
            onFinish={handleLogin}
            layout="vertical"
            size="large"
            initialValues={{ remember: true }}
          >
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please input your email!' },
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter your email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[{ required: true, message: 'Please input your password!' }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Enter your password"
              />
            </Form.Item>

            <Form.Item>
              <div className="flex justify-between items-center">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox>Remember me</Checkbox>
                </Form.Item>
                <Link to="/auth/forgot-password" className="text-primary-500 hover:text-primary-600">
                  Forgot password?
                </Link>
              </div>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="w-full"
              >
                Sign In
              </Button>
            </Form.Item>
          </Form>

          <Divider>Or continue with</Divider>

          <Space direction="vertical" className="w-full" size="middle">
            <Button
              icon={<GoogleOutlined />}
              className="w-full"
              size="large"
            >
              Continue with Google
            </Button>

            <Button
              icon={<GithubOutlined />}
              className="w-full"
              size="large"
            >
              Continue with GitHub
            </Button>
          </Space>

          <div className="text-center mt-6">
            <Text className="text-gray-600">
              Don't have an account?{' '}
              <Link to="/auth/register" className="text-primary-500 hover:text-primary-600 font-medium">
                Sign up
              </Link>
            </Text>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default LoginPage;

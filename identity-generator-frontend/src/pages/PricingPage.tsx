import { <PERSON>, <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON>, <PERSON>, Badge, Space, List } from 'antd';
import { CheckOutlined, CrownOutlined, StarOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { SUBSCRIPTION_PLANS } from '../constants';

const { Title, Text } = Typography;

const PricingPage: React.FC = () => {
  const handleSelectPlan = (planId: string) => {
    console.log('Selected plan:', planId);
    // TODO: Implement plan selection logic
  };

  return (
    <div className="space-y-12">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center"
      >
        <Title level={1} className="mb-4">Choose Your Plan</Title>
        <Text className="text-lg text-gray-600 max-w-2xl mx-auto">
          Select the perfect plan for your identity generation needs.
          Upgrade or downgrade at any time with no long-term commitments.
        </Text>
      </motion.div>

      {/* Pricing Cards */}
      <Row gutter={[24, 24]} justify="center">
        {SUBSCRIPTION_PLANS.map((plan, index) => (
          <Col xs={24} sm={12} lg={6} key={plan.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="h-full"
            >
              <Card
                className={`h-full card-shadow hover:card-shadow-lg transition-all duration-300 relative ${
                  plan.popular ? 'border-primary-500 border-2' : ''
                }`}
              >
                {plan.popular && (
                  <Badge.Ribbon text="Most Popular" color="gold">
                    <div className="absolute top-0 left-0 w-full h-full pointer-events-none" />
                  </Badge.Ribbon>
                )}

                <div className="text-center mb-6">
                  <div className="mb-4">
                    {plan.id === 'free' && <StarOutlined className="text-4xl text-gray-500" />}
                    {plan.id === 'personal' && <CheckOutlined className="text-4xl text-primary-500" />}
                    {plan.id === 'professional' && <CrownOutlined className="text-4xl text-warning-500" />}
                    {plan.id === 'enterprise' && <CrownOutlined className="text-4xl text-purple-500" />}
                  </div>

                  <Title level={3} className="mb-2">{plan.name}</Title>

                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">
                      ${plan.price}
                    </span>
                    {plan.price > 0 && (
                      <span className="text-gray-500 ml-1">/{plan.period}</span>
                    )}
                  </div>

                  <Button
                    type={plan.popular ? 'primary' : 'default'}
                    size="large"
                    onClick={() => handleSelectPlan(plan.id)}
                    className="w-full mb-6"
                  >
                    {plan.id === 'free' ? 'Get Started' : 'Choose Plan'}
                  </Button>
                </div>

                <div className="space-y-4">
                  <div>
                    <Text strong className="text-gray-900">Features:</Text>
                    <List
                      size="small"
                      dataSource={plan.features}
                      renderItem={(feature) => (
                        <List.Item className="border-none py-1">
                          <Space>
                            <CheckOutlined className="text-success-500" />
                            <Text className="text-gray-700">{feature}</Text>
                          </Space>
                        </List.Item>
                      )}
                    />
                  </div>

                  {plan.limitations && (
                    <div>
                      <Text strong className="text-gray-900">Limitations:</Text>
                      <List
                        size="small"
                        dataSource={plan.limitations}
                        renderItem={(limitation) => (
                          <List.Item className="border-none py-1">
                            <Text className="text-gray-500">• {limitation}</Text>
                          </List.Item>
                        )}
                      />
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          </Col>
        ))}
      </Row>

      {/* FAQ Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="text-center"
      >
        <Title level={2} className="mb-8">Frequently Asked Questions</Title>

        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card className="text-left card-shadow">
              <Title level={4}>Can I change my plan anytime?</Title>
              <Text className="text-gray-600">
                Yes, you can upgrade or downgrade your plan at any time.
                Changes take effect immediately and billing is prorated.
              </Text>
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card className="text-left card-shadow">
              <Title level={4}>Is there a free trial?</Title>
              <Text className="text-gray-600">
                Our free plan gives you 5 generations per day with no time limit.
                You can upgrade to paid plans for more features and higher limits.
              </Text>
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card className="text-left card-shadow">
              <Title level={4}>What payment methods do you accept?</Title>
              <Text className="text-gray-600">
                We accept all major credit cards, PayPal, and bank transfers for enterprise plans.
                All payments are processed securely through Stripe.
              </Text>
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card className="text-left card-shadow">
              <Title level={4}>Do you offer refunds?</Title>
              <Text className="text-gray-600">
                Yes, we offer a 30-day money-back guarantee for all paid plans.
                Contact our support team if you're not satisfied.
              </Text>
            </Card>
          </Col>
        </Row>
      </motion.div>

      {/* CTA Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        className="text-center py-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl text-white"
      >
        <Title level={2} className="text-white mb-4">
          Ready to Get Started?
        </Title>
        <Text className="text-primary-100 text-lg mb-8 max-w-2xl mx-auto">
          Join thousands of developers and testers who trust Identity Generator Pro
          for their virtual identity generation needs.
        </Text>
        <Space size="large">
          <Button
            size="large"
            className="bg-white text-primary-600 border-white hover:bg-gray-100"
            onClick={() => handleSelectPlan('free')}
          >
            Start Free
          </Button>
          <Button
            type="primary"
            size="large"
            className="bg-primary-700 border-primary-700 hover:bg-primary-800"
            onClick={() => handleSelectPlan('professional')}
          >
            Go Professional
          </Button>
        </Space>
      </motion.div>
    </div>
  );
};

export default PricingPage;

import { useState } from 'react';
import { Form, Input, Button, Card, Typography, Divider, Space, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, GoogleOutlined, GithubOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAppDispatch } from '../store';
import { setUser } from '../store/slices/userSlice';
import { addNotification } from '../store/slices/uiSlice';

const { Title, Text } = Typography;

interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

const RegisterPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleRegister = async (values: RegisterFormData) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock user data
      const mockUser = {
        id: '1',
        email: values.email,
        username: values.username,
        firstName: values.firstName,
        lastName: values.lastName,
        plan: 'free' as const,
        subscription: {
          isActive: true,
          generationsLeft: 5,
          generationsLimit: 5,
        },
        preferences: {
          defaultCountry: 'US',
          defaultGender: 'random' as const,
          theme: 'light' as const,
          language: 'en',
        },
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      };

      dispatch(setUser(mockUser));
      dispatch(addNotification({
        type: 'success',
        title: 'Account Created!',
        message: 'Welcome to Identity Generator Pro! Your account has been created successfully.',
      }));

      navigate('/');
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Registration Failed',
        message: 'Something went wrong. Please try again.',
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md"
      >
        <Card className="card-shadow-lg">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <UserOutlined className="text-white text-2xl" />
            </div>
            <Title level={2} className="mb-2">Create Account</Title>
            <Text className="text-gray-600">
              Join Identity Generator Pro today
            </Text>
          </div>

          <Form
            name="register"
            onFinish={handleRegister}
            layout="vertical"
            size="large"
          >
            <div className="grid grid-cols-2 gap-4">
              <Form.Item
                name="firstName"
                label="First Name"
                rules={[{ required: true, message: 'Please input your first name!' }]}
              >
                <Input placeholder="First name" />
              </Form.Item>

              <Form.Item
                name="lastName"
                label="Last Name"
                rules={[{ required: true, message: 'Please input your last name!' }]}
              >
                <Input placeholder="Last name" />
              </Form.Item>
            </div>

            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true, message: 'Please input your username!' },
                { min: 3, message: 'Username must be at least 3 characters!' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Choose a username"
              />
            </Form.Item>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please input your email!' },
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Enter your email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please input your password!' },
                { min: 8, message: 'Password must be at least 8 characters!' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Create a password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="Confirm Password"
              dependencies={['password']}
              rules={[
                { required: true, message: 'Please confirm your password!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Passwords do not match!'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Confirm your password"
              />
            </Form.Item>

            <Form.Item
              name="agreeToTerms"
              valuePropName="checked"
              rules={[
                { required: true, message: 'Please agree to the terms and conditions!' }
              ]}
            >
              <Checkbox>
                I agree to the{' '}
                <Link to="/terms" className="text-primary-500 hover:text-primary-600">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link to="/privacy" className="text-primary-500 hover:text-primary-600">
                  Privacy Policy
                </Link>
              </Checkbox>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="w-full"
              >
                Create Account
              </Button>
            </Form.Item>
          </Form>

          <Divider>Or continue with</Divider>

          <Space direction="vertical" className="w-full" size="middle">
            <Button
              icon={<GoogleOutlined />}
              className="w-full"
              size="large"
            >
              Continue with Google
            </Button>

            <Button
              icon={<GithubOutlined />}
              className="w-full"
              size="large"
            >
              Continue with GitHub
            </Button>
          </Space>

          <div className="text-center mt-6">
            <Text className="text-gray-600">
              Already have an account?{' '}
              <Link to="/auth/login" className="text-primary-500 hover:text-primary-600 font-medium">
                Sign in
              </Link>
            </Text>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default RegisterPage;

import { useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store';
import {
  setCurrentIdentity,
  setGeneratedIdentities,
  setGenerating,
  setError,
  clearError,
} from '../store/slices/identitySlice';
import { addNotification } from '../store/slices/uiSlice';
import apiService from '../services/api';
import { GenerationOptions, IdentityData } from '../types';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '../constants';

export const useIdentityGenerator = () => {
  const dispatch = useAppDispatch();
  const { generationOptions, isGenerating, error } = useAppSelector((state) => state.identity);
  const [countries, setCountries] = useState<any[]>([]);

  const generateSingleIdentity = useCallback(async (options?: Partial<GenerationOptions>) => {
    try {
      dispatch(setGenerating(true));
      dispatch(clearError());

      const finalOptions: GenerationOptions = {
        ...generationOptions,
        ...options,
        quantity: 1,
      };

      const identity = await apiService.generateIdentity(finalOptions);
      
      dispatch(setCurrentIdentity(identity));
      dispatch(addNotification({
        type: 'success',
        title: 'Success',
        message: SUCCESS_MESSAGES.IDENTITY_GENERATED,
      }));

      return identity;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || ERROR_MESSAGES.NETWORK_ERROR;
      dispatch(setError(errorMessage));
      dispatch(addNotification({
        type: 'error',
        title: 'Generation Failed',
        message: errorMessage,
      }));
      throw error;
    } finally {
      dispatch(setGenerating(false));
    }
  }, [dispatch, generationOptions]);

  const generateMultipleIdentities = useCallback(async (options?: Partial<GenerationOptions>) => {
    try {
      dispatch(setGenerating(true));
      dispatch(clearError());

      const finalOptions: GenerationOptions = {
        ...generationOptions,
        ...options,
      };

      const identities = await apiService.generateMultipleIdentities(finalOptions);
      
      dispatch(setGeneratedIdentities(identities));
      dispatch(addNotification({
        type: 'success',
        title: 'Success',
        message: `${identities.length} identities generated successfully!`,
      }));

      return identities;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || ERROR_MESSAGES.NETWORK_ERROR;
      dispatch(setError(errorMessage));
      dispatch(addNotification({
        type: 'error',
        title: 'Generation Failed',
        message: errorMessage,
      }));
      throw error;
    } finally {
      dispatch(setGenerating(false));
    }
  }, [dispatch, generationOptions]);

  const loadSupportedCountries = useCallback(async () => {
    try {
      const supportedCountries = await apiService.getSupportedCountries();
      setCountries(supportedCountries);
      return supportedCountries;
    } catch (error: any) {
      console.error('Failed to load countries:', error);
      dispatch(addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load supported countries',
      }));
      return [];
    }
  }, [dispatch]);

  const testConnection = useCallback(async () => {
    try {
      await apiService.healthCheck();
      dispatch(addNotification({
        type: 'success',
        title: 'Connection Test',
        message: 'Backend connection successful!',
      }));
      return true;
    } catch (error: any) {
      dispatch(addNotification({
        type: 'error',
        title: 'Connection Test',
        message: 'Failed to connect to backend server',
      }));
      return false;
    }
  }, [dispatch]);

  return {
    // State
    isGenerating,
    error,
    countries,
    
    // Actions
    generateSingleIdentity,
    generateMultipleIdentities,
    loadSupportedCountries,
    testConnection,
  };
};

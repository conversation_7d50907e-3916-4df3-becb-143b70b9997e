import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { API_BASE_URL, TOKEN_KEY } from '../constants';
import { ApiResponse, IdentityData, GenerationOptions, Country } from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem(TOKEN_KEY);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem(TOKEN_KEY);
          window.location.href = '/auth/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Identity generation methods
  async generateIdentity(options: GenerationOptions): Promise<IdentityData> {
    const response = await this.api.post<ApiResponse<IdentityData>>('/identity/generate', options);
    return response.data.data!;
  }

  async generateMultipleIdentities(options: GenerationOptions): Promise<IdentityData[]> {
    const response = await this.api.post<ApiResponse<IdentityData[]>>('/identity/generate-batch', options);
    return response.data.data!;
  }

  async getSupportedCountries(): Promise<Country[]> {
    const response = await this.api.get<ApiResponse<Country[]>>('/identity/countries');
    return response.data.data!;
  }

  async getCountryFormats(countryCode: string): Promise<Country> {
    const response = await this.api.get<ApiResponse<Country>>(`/identity/formats/${countryCode}`);
    return response.data.data!;
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.api.get<ApiResponse>('/health');
    return response.data.data;
  }

  // Generic API methods
  async get<T>(url: string): Promise<T> {
    const response = await this.api.get<ApiResponse<T>>(url);
    return response.data.data!;
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.post<ApiResponse<T>>(url, data);
    return response.data.data!;
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<ApiResponse<T>>(url, data);
    return response.data.data!;
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<ApiResponse<T>>(url);
    return response.data.data!;
  }
}

export default new ApiService();

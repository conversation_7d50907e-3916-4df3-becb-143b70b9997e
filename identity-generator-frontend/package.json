{"name": "identity-generator-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "preview": "vite preview", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.10", "antd": "^5.26.3", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "framer-motion": "^12.23.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}
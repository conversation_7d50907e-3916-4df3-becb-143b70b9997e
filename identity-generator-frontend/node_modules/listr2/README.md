# listr2

[![Pipeline](https://gitlab.kilic.dev/libraries/listr2/badges/master/pipeline.svg?style=flat-square&ignore_skipped=true)](https://gitlab.kilic.dev/libraries/listr2/-/commits/master) [![Version](https://img.shields.io/npm/v/listr2.svg?style=flat-square&logo=npm)](https://www.npmjs.com/package/listr2?activeTab=versions) [![Downloads](https://img.shields.io/npm/dm/listr2.svg?style=flat-square&logo=npm)](https://www.npmjs.com/package/listr2) [![Size](https://img.shields.io/bundlephobia/min/listr2?style=flat-square&logo=npm)](https://www.npmjs.com/package/listr2) [![Dependencies](https://img.shields.io/librariesio/release/npm/listr2?style=flat-square&logo=npm)](https://www.npmjs.com/package/listr2?activeTab=dependencies) [![codecov](https://codecov.io/gh/listr2/listr2/branch/master/graph/badge.svg?style=flat-square)](https://codecov.io/gh/listr2/listr2)

[![github sponsors](https://img.shields.io/github/sponsors/cenk1cenk2?style=flat-square&logo=github)](https://github.com/sponsors/cenk1cenk2) [![opencollective](https://img.shields.io/opencollective/sponsors/listr2?label=open%20collective&logo=opencollective)](https://opencollective.com/listr2)

**Create beautiful CLI interfaces via easy and logical to-implement task lists that feel alive and interactive.**

---

## Documentation

**[Read the documentation...](https://listr2.kilic.dev)**

## Demo

![Demo](https://media.githubusercontent.com/media/listr2/listr2/master/examples/renderer-default.gif)

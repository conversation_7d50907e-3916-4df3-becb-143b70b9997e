(function(n){function e(){var i=n();return i.default||i}if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var t=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};t.prettierPlugins=t.prettierPlugins||{},t.prettierPlugins.markdown=e()}})(function(){"use strict";var yl=Object.create;var kr=Object.defineProperty;var wl=Object.getOwnPropertyDescriptor;var xl=Object.getOwnPropertyNames;var kl=Object.getPrototypeOf,Bl=Object.prototype.hasOwnProperty;var Gn=e=>{throw TypeError(e)};var C=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Yn=(e,r)=>{for(var t in r)kr(e,t,{get:r[t],enumerable:!0})},$n=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let a of xl(r))!Bl.call(e,a)&&a!==t&&kr(e,a,{get:()=>r[a],enumerable:!(n=wl(r,a))||n.enumerable});return e};var Me=(e,r,t)=>(t=e!=null?yl(kl(e)):{},$n(r||!e||!e.__esModule?kr(t,"default",{value:e,enumerable:!0}):t,e)),Tl=e=>$n(kr({},"__esModule",{value:!0}),e);var Vn=(e,r,t)=>r.has(e)||Gn("Cannot "+t);var ce=(e,r,t)=>(Vn(e,r,"read from private field"),t?t.call(e):r.get(e)),jn=(e,r,t)=>r.has(e)?Gn("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(e):r.set(e,t),Wn=(e,r,t,n)=>(Vn(e,r,"write to private field"),n?n.call(e,t):r.set(e,t),t);var Br=C((cF,Hn)=>{"use strict";Hn.exports=Sl;function Sl(e){return String(e).replace(/\s+/g," ")}});var iu=C((cv,nu)=>{"use strict";nu.exports=Vf;var Dr=9,Gr=10,je=32,zf=33,Gf=58,We=91,Yf=92,Pt=93,pr=94,Yr=96,$r=4,$f=1024;function Vf(e){var r=this.Parser,t=this.Compiler;jf(r)&&Hf(r,e),Wf(t)&&Kf(t)}function jf(e){return!!(e&&e.prototype&&e.prototype.blockTokenizers)}function Wf(e){return!!(e&&e.prototype&&e.prototype.visitors)}function Hf(e,r){for(var t=r||{},n=e.prototype,a=n.blockTokenizers,i=n.inlineTokenizers,u=n.blockMethods,o=n.inlineMethods,s=a.definition,l=i.reference,c=[],f=-1,p=u.length,d;++f<p;)d=u[f],!(d==="newline"||d==="indentedCode"||d==="paragraph"||d==="footnoteDefinition")&&c.push([d]);c.push(["footnoteDefinition"]),t.inlineNotes&&(Ot(o,"reference","inlineNote"),i.inlineNote=m),Ot(u,"definition","footnoteDefinition"),Ot(o,"reference","footnoteCall"),a.definition=A,a.footnoteDefinition=D,i.footnoteCall=h,i.reference=F,n.interruptFootnoteDefinition=c,F.locator=l.locator,h.locator=v,m.locator=B;function D(b,g,y){for(var w=this,E=w.interruptFootnoteDefinition,x=w.offset,k=g.length+1,T=0,q=[],N,P,S,_,O,Be,W,I,ee,Z,Ee,ve,U;T<k&&(_=g.charCodeAt(T),!(_!==Dr&&_!==je));)T++;if(g.charCodeAt(T++)===We&&g.charCodeAt(T++)===pr){for(P=T;T<k;){if(_=g.charCodeAt(T),_!==_||_===Gr||_===Dr||_===je)return;if(_===Pt){S=T,T++;break}T++}if(!(S===void 0||P===S||g.charCodeAt(T++)!==Gf)){if(y)return!0;for(N=g.slice(P,S),O=b.now(),ee=0,Z=0,Ee=T,ve=[];T<k;){if(_=g.charCodeAt(T),_!==_||_===Gr)U={start:ee,contentStart:Ee||T,contentEnd:T,end:T},ve.push(U),_===Gr&&(ee=T+1,Z=0,Ee=void 0,U.end=ee);else if(Z!==void 0)if(_===je||_===Dr)Z+=_===je?1:$r-Z%$r,Z>$r&&(Z=void 0,Ee=T);else{if(Z<$r&&U&&(U.contentStart===U.contentEnd||Xf(E,a,w,[b,g.slice(T,$f),!0])))break;Z=void 0,Ee=T}T++}for(T=-1,k=ve.length;k>0&&(U=ve[k-1],U.contentStart===U.contentEnd);)k--;for(Be=b(g.slice(0,U.contentEnd));++T<k;)U=ve[T],x[O.line+T]=(x[O.line+T]||0)+(U.contentStart-U.start),q.push(g.slice(U.contentStart,U.end));return W=w.enterBlock(),I=w.tokenizeBlock(q.join(""),O),W(),Be({type:"footnoteDefinition",identifier:N.toLowerCase(),label:N,children:I})}}}function h(b,g,y){var w=g.length+1,E=0,x,k,T,q;if(g.charCodeAt(E++)===We&&g.charCodeAt(E++)===pr){for(k=E;E<w;){if(q=g.charCodeAt(E),q!==q||q===Gr||q===Dr||q===je)return;if(q===Pt){T=E,E++;break}E++}if(!(T===void 0||k===T))return y?!0:(x=g.slice(k,T),b(g.slice(0,E))({type:"footnoteReference",identifier:x.toLowerCase(),label:x}))}}function m(b,g,y){var w=this,E=g.length+1,x=0,k=0,T,q,N,P,S,_,O;if(g.charCodeAt(x++)===pr&&g.charCodeAt(x++)===We){for(N=x;x<E;){if(q=g.charCodeAt(x),q!==q)return;if(_===void 0)if(q===Yf)x+=2;else if(q===We)k++,x++;else if(q===Pt)if(k===0){P=x,x++;break}else k--,x++;else if(q===Yr){for(S=x,_=1;g.charCodeAt(S+_)===Yr;)_++;x+=_}else x++;else if(q===Yr){for(S=x,O=1;g.charCodeAt(S+O)===Yr;)O++;x+=O,_===O&&(_=void 0),O=void 0}else x++}if(P!==void 0)return y?!0:(T=b.now(),T.column+=2,T.offset+=2,b(g.slice(0,x))({type:"footnote",children:w.tokenizeInline(g.slice(N,P),T)}))}}function F(b,g,y){var w=0;if(g.charCodeAt(w)===zf&&w++,g.charCodeAt(w)===We&&g.charCodeAt(w+1)!==pr)return l.call(this,b,g,y)}function A(b,g,y){for(var w=0,E=g.charCodeAt(w);E===je||E===Dr;)E=g.charCodeAt(++w);if(E===We&&g.charCodeAt(w+1)!==pr)return s.call(this,b,g,y)}function v(b,g){return b.indexOf("[",g)}function B(b,g){return b.indexOf("^[",g)}}function Kf(e){var r=e.prototype.visitors,t="    ";r.footnote=n,r.footnoteReference=a,r.footnoteDefinition=i;function n(u){return"^["+this.all(u).join("")+"]"}function a(u){return"[^"+(u.label||u.identifier)+"]"}function i(u){for(var o=this.all(u).join(`

`).split(`
`),s=0,l=o.length,c;++s<l;)c=o[s],c!==""&&(o[s]=t+c);return"[^"+(u.label||u.identifier)+"]: "+o.join(`
`)}}function Ot(e,r,t){e.splice(e.indexOf(r),0,t)}function Xf(e,r,t,n){for(var a=e.length,i=-1;++i<a;)if(r[e[i][0]].apply(t,n))return!0;return!1}});var It=C(Lt=>{Lt.isRemarkParser=Jf;Lt.isRemarkCompiler=Qf;function Jf(e){return!!(e&&e.prototype&&e.prototype.blockTokenizers)}function Qf(e){return!!(e&&e.prototype&&e.prototype.visitors)}});var fu=C((fv,lu)=>{var uu=It();lu.exports=tD;var au=9,ou=32,Vr=36,Zf=48,eD=57,su=92,rD=["math","math-inline"],cu="math-display";function tD(e){let r=this.Parser,t=this.Compiler;uu.isRemarkParser(r)&&nD(r,e),uu.isRemarkCompiler(t)&&iD(t,e)}function nD(e,r){let t=e.prototype,n=t.inlineMethods;i.locator=a,t.inlineTokenizers.math=i,n.splice(n.indexOf("text"),0,"math");function a(u,o){return u.indexOf("$",o)}function i(u,o,s){let l=o.length,c=!1,f=!1,p=0,d,D,h,m,F,A,v;if(o.charCodeAt(p)===su&&(f=!0,p++),o.charCodeAt(p)===Vr){if(p++,f)return s?!0:u(o.slice(0,p))({type:"text",value:"$"});if(o.charCodeAt(p)===Vr&&(c=!0,p++),h=o.charCodeAt(p),!(h===ou||h===au)){for(m=p;p<l;){if(D=h,h=o.charCodeAt(p+1),D===Vr){if(d=o.charCodeAt(p-1),d!==ou&&d!==au&&(h!==h||h<Zf||h>eD)&&(!c||h===Vr)){F=p-1,p++,c&&p++,A=p;break}}else D===su&&(p++,h=o.charCodeAt(p+1));p++}if(A!==void 0)return s?!0:(v=o.slice(m,F+1),u(o.slice(0,A))({type:"inlineMath",value:v,data:{hName:"span",hProperties:{className:rD.concat(c&&r.inlineMathDouble?[cu]:[])},hChildren:[{type:"text",value:v}]}}))}}}}function iD(e){let r=e.prototype;r.visitors.inlineMath=t;function t(n){let a="$";return(n.data&&n.data.hProperties&&n.data.hProperties.className||[]).includes(cu)&&(a="$$"),a+n.value+a}}});var mu=C((Dv,du)=>{var Du=It();du.exports=sD;var pu=10,hr=32,Rt=36,hu=`
`,uD="$",aD=2,oD=["math","math-display"];function sD(){let e=this.Parser,r=this.Compiler;Du.isRemarkParser(e)&&cD(e),Du.isRemarkCompiler(r)&&lD(r)}function cD(e){let r=e.prototype,t=r.blockMethods,n=r.interruptParagraph,a=r.interruptList,i=r.interruptBlockquote;r.blockTokenizers.math=u,t.splice(t.indexOf("fencedCode")+1,0,"math"),n.splice(n.indexOf("fencedCode")+1,0,["math"]),a.splice(a.indexOf("fencedCode")+1,0,["math"]),i.splice(i.indexOf("fencedCode")+1,0,["math"]);function u(o,s,l){var c=s.length,f=0;let p,d,D,h,m,F,A,v,B,b,g;for(;f<c&&s.charCodeAt(f)===hr;)f++;for(m=f;f<c&&s.charCodeAt(f)===Rt;)f++;if(F=f-m,!(F<aD)){for(;f<c&&s.charCodeAt(f)===hr;)f++;for(A=f;f<c;){if(p=s.charCodeAt(f),p===Rt)return;if(p===pu)break;f++}if(s.charCodeAt(f)===pu){if(l)return!0;for(d=[],A!==f&&d.push(s.slice(A,f)),f++,D=s.indexOf(hu,f+1),D=D===-1?c:D;f<c;){for(v=!1,b=f,g=D,h=D,B=0;h>b&&s.charCodeAt(h-1)===hr;)h--;for(;h>b&&s.charCodeAt(h-1)===Rt;)B++,h--;for(F<=B&&s.indexOf(uD,b)===h&&(v=!0,g=h);b<=g&&b-f<m&&s.charCodeAt(b)===hr;)b++;if(v)for(;g>b&&s.charCodeAt(g-1)===hr;)g--;if((!v||b!==g)&&d.push(s.slice(b,g)),v)break;f=D+1,D=s.indexOf(hu,f+1),D=D===-1?c:D}return d=d.join(`
`),o(s.slice(0,D))({type:"math",value:d,data:{hName:"div",hProperties:{className:oD.concat()},hChildren:[{type:"text",value:d}]}})}}}}function lD(e){let r=e.prototype;r.visitors.math=t;function t(n){return`$$
`+n.value+`
$$`}}});var gu=C((pv,Fu)=>{var fD=fu(),DD=mu();Fu.exports=pD;function pD(e){var r=e||{};DD.call(this,r),fD.call(this,r)}});var Ie=C((hv,Eu)=>{Eu.exports=dD;var hD=Object.prototype.hasOwnProperty;function dD(){for(var e={},r=0;r<arguments.length;r++){var t=arguments[r];for(var n in t)hD.call(t,n)&&(e[n]=t[n])}return e}});var vu=C((dv,Nt)=>{typeof Object.create=="function"?Nt.exports=function(r,t){t&&(r.super_=t,r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}))}:Nt.exports=function(r,t){if(t){r.super_=t;var n=function(){};n.prototype=t.prototype,r.prototype=new n,r.prototype.constructor=r}}});var Au=C((mv,bu)=>{"use strict";var mD=Ie(),Cu=vu();bu.exports=FD;function FD(e){var r,t,n;Cu(i,e),Cu(a,i),r=i.prototype;for(t in r)n=r[t],n&&typeof n=="object"&&(r[t]="concat"in n?n.concat():mD(n));return i;function a(u){return e.apply(this,u)}function i(){return this instanceof i?e.apply(this,arguments):new a(arguments)}}});var wu=C((Fv,yu)=>{"use strict";yu.exports=gD;function gD(e,r,t){return n;function n(){var a=t||this,i=a[e];return a[e]=!r,u;function u(){a[e]=i}}}});var ku=C((gv,xu)=>{"use strict";xu.exports=ED;function ED(e){for(var r=String(e),t=[],n=/\r?\n|\r/g;n.exec(r);)t.push(n.lastIndex);return t.push(r.length+1),{toPoint:a,toPosition:a,toOffset:i};function a(u){var o=-1;if(u>-1&&u<t[t.length-1]){for(;++o<t.length;)if(t[o]>u)return{line:o+1,column:u-(t[o-1]||0)+1,offset:u}}return{}}function i(u){var o=u&&u.line,s=u&&u.column,l;return!isNaN(o)&&!isNaN(s)&&o-1 in t&&(l=(t[o-2]||0)+s-1||0),l>-1&&l<t[t.length-1]?l:-1}}});var Tu=C((Ev,Bu)=>{"use strict";Bu.exports=vD;var Mt="\\";function vD(e,r){return t;function t(n){for(var a=0,i=n.indexOf(Mt),u=e[r],o=[],s;i!==-1;)o.push(n.slice(a,i)),a=i+1,s=n.charAt(a),(!s||u.indexOf(s)===-1)&&o.push(Mt),i=n.indexOf(Mt,a+1);return o.push(n.slice(a)),o.join("")}}});var qu=C((vv,CD)=>{CD.exports={AElig:"\xC6",AMP:"&",Aacute:"\xC1",Acirc:"\xC2",Agrave:"\xC0",Aring:"\xC5",Atilde:"\xC3",Auml:"\xC4",COPY:"\xA9",Ccedil:"\xC7",ETH:"\xD0",Eacute:"\xC9",Ecirc:"\xCA",Egrave:"\xC8",Euml:"\xCB",GT:">",Iacute:"\xCD",Icirc:"\xCE",Igrave:"\xCC",Iuml:"\xCF",LT:"<",Ntilde:"\xD1",Oacute:"\xD3",Ocirc:"\xD4",Ograve:"\xD2",Oslash:"\xD8",Otilde:"\xD5",Ouml:"\xD6",QUOT:'"',REG:"\xAE",THORN:"\xDE",Uacute:"\xDA",Ucirc:"\xDB",Ugrave:"\xD9",Uuml:"\xDC",Yacute:"\xDD",aacute:"\xE1",acirc:"\xE2",acute:"\xB4",aelig:"\xE6",agrave:"\xE0",amp:"&",aring:"\xE5",atilde:"\xE3",auml:"\xE4",brvbar:"\xA6",ccedil:"\xE7",cedil:"\xB8",cent:"\xA2",copy:"\xA9",curren:"\xA4",deg:"\xB0",divide:"\xF7",eacute:"\xE9",ecirc:"\xEA",egrave:"\xE8",eth:"\xF0",euml:"\xEB",frac12:"\xBD",frac14:"\xBC",frac34:"\xBE",gt:">",iacute:"\xED",icirc:"\xEE",iexcl:"\xA1",igrave:"\xEC",iquest:"\xBF",iuml:"\xEF",laquo:"\xAB",lt:"<",macr:"\xAF",micro:"\xB5",middot:"\xB7",nbsp:"\xA0",not:"\xAC",ntilde:"\xF1",oacute:"\xF3",ocirc:"\xF4",ograve:"\xF2",ordf:"\xAA",ordm:"\xBA",oslash:"\xF8",otilde:"\xF5",ouml:"\xF6",para:"\xB6",plusmn:"\xB1",pound:"\xA3",quot:'"',raquo:"\xBB",reg:"\xAE",sect:"\xA7",shy:"\xAD",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",szlig:"\xDF",thorn:"\xFE",times:"\xD7",uacute:"\xFA",ucirc:"\xFB",ugrave:"\xF9",uml:"\xA8",uuml:"\xFC",yacute:"\xFD",yen:"\xA5",yuml:"\xFF"}});var _u=C((Cv,bD)=>{bD.exports={"0":"\uFFFD","128":"\u20AC","130":"\u201A","131":"\u0192","132":"\u201E","133":"\u2026","134":"\u2020","135":"\u2021","136":"\u02C6","137":"\u2030","138":"\u0160","139":"\u2039","140":"\u0152","142":"\u017D","145":"\u2018","146":"\u2019","147":"\u201C","148":"\u201D","149":"\u2022","150":"\u2013","151":"\u2014","152":"\u02DC","153":"\u2122","154":"\u0161","155":"\u203A","156":"\u0153","158":"\u017E","159":"\u0178"}});var Re=C((bv,Su)=>{"use strict";Su.exports=AD;function AD(e){var r=typeof e=="string"?e.charCodeAt(0):e;return r>=48&&r<=57}});var Ou=C((Av,Pu)=>{"use strict";Pu.exports=yD;function yD(e){var r=typeof e=="string"?e.charCodeAt(0):e;return r>=97&&r<=102||r>=65&&r<=70||r>=48&&r<=57}});var He=C((yv,Lu)=>{"use strict";Lu.exports=wD;function wD(e){var r=typeof e=="string"?e.charCodeAt(0):e;return r>=97&&r<=122||r>=65&&r<=90}});var Ru=C((wv,Iu)=>{"use strict";var xD=He(),kD=Re();Iu.exports=BD;function BD(e){return xD(e)||kD(e)}});var Nu=C((xv,TD)=>{TD.exports={AEli:"\xC6",AElig:"\xC6",AM:"&",AMP:"&",Aacut:"\xC1",Aacute:"\xC1",Abreve:"\u0102",Acir:"\xC2",Acirc:"\xC2",Acy:"\u0410",Afr:"\u{1D504}",Agrav:"\xC0",Agrave:"\xC0",Alpha:"\u0391",Amacr:"\u0100",And:"\u2A53",Aogon:"\u0104",Aopf:"\u{1D538}",ApplyFunction:"\u2061",Arin:"\xC5",Aring:"\xC5",Ascr:"\u{1D49C}",Assign:"\u2254",Atild:"\xC3",Atilde:"\xC3",Aum:"\xC4",Auml:"\xC4",Backslash:"\u2216",Barv:"\u2AE7",Barwed:"\u2306",Bcy:"\u0411",Because:"\u2235",Bernoullis:"\u212C",Beta:"\u0392",Bfr:"\u{1D505}",Bopf:"\u{1D539}",Breve:"\u02D8",Bscr:"\u212C",Bumpeq:"\u224E",CHcy:"\u0427",COP:"\xA9",COPY:"\xA9",Cacute:"\u0106",Cap:"\u22D2",CapitalDifferentialD:"\u2145",Cayleys:"\u212D",Ccaron:"\u010C",Ccedi:"\xC7",Ccedil:"\xC7",Ccirc:"\u0108",Cconint:"\u2230",Cdot:"\u010A",Cedilla:"\xB8",CenterDot:"\xB7",Cfr:"\u212D",Chi:"\u03A7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",Colon:"\u2237",Colone:"\u2A74",Congruent:"\u2261",Conint:"\u222F",ContourIntegral:"\u222E",Copf:"\u2102",Coproduct:"\u2210",CounterClockwiseContourIntegral:"\u2233",Cross:"\u2A2F",Cscr:"\u{1D49E}",Cup:"\u22D3",CupCap:"\u224D",DD:"\u2145",DDotrahd:"\u2911",DJcy:"\u0402",DScy:"\u0405",DZcy:"\u040F",Dagger:"\u2021",Darr:"\u21A1",Dashv:"\u2AE4",Dcaron:"\u010E",Dcy:"\u0414",Del:"\u2207",Delta:"\u0394",Dfr:"\u{1D507}",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",Diamond:"\u22C4",DifferentialD:"\u2146",Dopf:"\u{1D53B}",Dot:"\xA8",DotDot:"\u20DC",DotEqual:"\u2250",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVector:"\u21BD",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295F",DownRightVector:"\u21C1",DownRightVectorBar:"\u2957",DownTee:"\u22A4",DownTeeArrow:"\u21A7",Downarrow:"\u21D3",Dscr:"\u{1D49F}",Dstrok:"\u0110",ENG:"\u014A",ET:"\xD0",ETH:"\xD0",Eacut:"\xC9",Eacute:"\xC9",Ecaron:"\u011A",Ecir:"\xCA",Ecirc:"\xCA",Ecy:"\u042D",Edot:"\u0116",Efr:"\u{1D508}",Egrav:"\xC8",Egrave:"\xC8",Element:"\u2208",Emacr:"\u0112",EmptySmallSquare:"\u25FB",EmptyVerySmallSquare:"\u25AB",Eogon:"\u0118",Eopf:"\u{1D53C}",Epsilon:"\u0395",Equal:"\u2A75",EqualTilde:"\u2242",Equilibrium:"\u21CC",Escr:"\u2130",Esim:"\u2A73",Eta:"\u0397",Eum:"\xCB",Euml:"\xCB",Exists:"\u2203",ExponentialE:"\u2147",Fcy:"\u0424",Ffr:"\u{1D509}",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",Fopf:"\u{1D53D}",ForAll:"\u2200",Fouriertrf:"\u2131",Fscr:"\u2131",GJcy:"\u0403",G:">",GT:">",Gamma:"\u0393",Gammad:"\u03DC",Gbreve:"\u011E",Gcedil:"\u0122",Gcirc:"\u011C",Gcy:"\u0413",Gdot:"\u0120",Gfr:"\u{1D50A}",Gg:"\u22D9",Gopf:"\u{1D53E}",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",Gt:"\u226B",HARDcy:"\u042A",Hacek:"\u02C7",Hat:"^",Hcirc:"\u0124",Hfr:"\u210C",HilbertSpace:"\u210B",Hopf:"\u210D",HorizontalLine:"\u2500",Hscr:"\u210B",Hstrok:"\u0126",HumpDownHump:"\u224E",HumpEqual:"\u224F",IEcy:"\u0415",IJlig:"\u0132",IOcy:"\u0401",Iacut:"\xCD",Iacute:"\xCD",Icir:"\xCE",Icirc:"\xCE",Icy:"\u0418",Idot:"\u0130",Ifr:"\u2111",Igrav:"\xCC",Igrave:"\xCC",Im:"\u2111",Imacr:"\u012A",ImaginaryI:"\u2148",Implies:"\u21D2",Int:"\u222C",Integral:"\u222B",Intersection:"\u22C2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Iogon:"\u012E",Iopf:"\u{1D540}",Iota:"\u0399",Iscr:"\u2110",Itilde:"\u0128",Iukcy:"\u0406",Ium:"\xCF",Iuml:"\xCF",Jcirc:"\u0134",Jcy:"\u0419",Jfr:"\u{1D50D}",Jopf:"\u{1D541}",Jscr:"\u{1D4A5}",Jsercy:"\u0408",Jukcy:"\u0404",KHcy:"\u0425",KJcy:"\u040C",Kappa:"\u039A",Kcedil:"\u0136",Kcy:"\u041A",Kfr:"\u{1D50E}",Kopf:"\u{1D542}",Kscr:"\u{1D4A6}",LJcy:"\u0409",L:"<",LT:"<",Lacute:"\u0139",Lambda:"\u039B",Lang:"\u27EA",Laplacetrf:"\u2112",Larr:"\u219E",Lcaron:"\u013D",Lcedil:"\u013B",Lcy:"\u041B",LeftAngleBracket:"\u27E8",LeftArrow:"\u2190",LeftArrowBar:"\u21E4",LeftArrowRightArrow:"\u21C6",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21C3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230A",LeftRightArrow:"\u2194",LeftRightVector:"\u294E",LeftTee:"\u22A3",LeftTeeArrow:"\u21A4",LeftTeeVector:"\u295A",LeftTriangle:"\u22B2",LeftTriangleBar:"\u29CF",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21BF",LeftUpVectorBar:"\u2958",LeftVector:"\u21BC",LeftVectorBar:"\u2952",Leftarrow:"\u21D0",Leftrightarrow:"\u21D4",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",LessLess:"\u2AA1",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",Lfr:"\u{1D50F}",Ll:"\u22D8",Lleftarrow:"\u21DA",Lmidot:"\u013F",LongLeftArrow:"\u27F5",LongLeftRightArrow:"\u27F7",LongRightArrow:"\u27F6",Longleftarrow:"\u27F8",Longleftrightarrow:"\u27FA",Longrightarrow:"\u27F9",Lopf:"\u{1D543}",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",Lscr:"\u2112",Lsh:"\u21B0",Lstrok:"\u0141",Lt:"\u226A",Map:"\u2905",Mcy:"\u041C",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",MinusPlus:"\u2213",Mopf:"\u{1D544}",Mscr:"\u2133",Mu:"\u039C",NJcy:"\u040A",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041D",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,Nfr:"\u{1D511}",NoBreak:"\u2060",NonBreakingSpace:"\xA0",Nopf:"\u2115",Not:"\u2AEC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangle:"\u22EB",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",Nscr:"\u{1D4A9}",Ntild:"\xD1",Ntilde:"\xD1",Nu:"\u039D",OElig:"\u0152",Oacut:"\xD3",Oacute:"\xD3",Ocir:"\xD4",Ocirc:"\xD4",Ocy:"\u041E",Odblac:"\u0150",Ofr:"\u{1D512}",Ograv:"\xD2",Ograve:"\xD2",Omacr:"\u014C",Omega:"\u03A9",Omicron:"\u039F",Oopf:"\u{1D546}",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",Or:"\u2A54",Oscr:"\u{1D4AA}",Oslas:"\xD8",Oslash:"\xD8",Otild:"\xD5",Otilde:"\xD5",Otimes:"\u2A37",Oum:"\xD6",Ouml:"\xD6",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",PartialD:"\u2202",Pcy:"\u041F",Pfr:"\u{1D513}",Phi:"\u03A6",Pi:"\u03A0",PlusMinus:"\xB1",Poincareplane:"\u210C",Popf:"\u2119",Pr:"\u2ABB",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",Prime:"\u2033",Product:"\u220F",Proportion:"\u2237",Proportional:"\u221D",Pscr:"\u{1D4AB}",Psi:"\u03A8",QUO:'"',QUOT:'"',Qfr:"\u{1D514}",Qopf:"\u211A",Qscr:"\u{1D4AC}",RBarr:"\u2910",RE:"\xAE",REG:"\xAE",Racute:"\u0154",Rang:"\u27EB",Rarr:"\u21A0",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",Re:"\u211C",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",Rfr:"\u211C",Rho:"\u03A1",RightAngleBracket:"\u27E9",RightArrow:"\u2192",RightArrowBar:"\u21E5",RightArrowLeftArrow:"\u21C4",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVector:"\u21C2",RightDownVectorBar:"\u2955",RightFloor:"\u230B",RightTee:"\u22A2",RightTeeArrow:"\u21A6",RightTeeVector:"\u295B",RightTriangle:"\u22B3",RightTriangleBar:"\u29D0",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVector:"\u21BE",RightUpVectorBar:"\u2954",RightVector:"\u21C0",RightVectorBar:"\u2953",Rightarrow:"\u21D2",Ropf:"\u211D",RoundImplies:"\u2970",Rrightarrow:"\u21DB",Rscr:"\u211B",Rsh:"\u21B1",RuleDelayed:"\u29F4",SHCHcy:"\u0429",SHcy:"\u0428",SOFTcy:"\u042C",Sacute:"\u015A",Sc:"\u2ABC",Scaron:"\u0160",Scedil:"\u015E",Scirc:"\u015C",Scy:"\u0421",Sfr:"\u{1D516}",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",Sigma:"\u03A3",SmallCircle:"\u2218",Sopf:"\u{1D54A}",Sqrt:"\u221A",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Sscr:"\u{1D4AE}",Star:"\u22C6",Sub:"\u22D0",Subset:"\u22D0",SubsetEqual:"\u2286",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",SuchThat:"\u220B",Sum:"\u2211",Sup:"\u22D1",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22D1",THOR:"\xDE",THORN:"\xDE",TRADE:"\u2122",TSHcy:"\u040B",TScy:"\u0426",Tab:"	",Tau:"\u03A4",Tcaron:"\u0164",Tcedil:"\u0162",Tcy:"\u0422",Tfr:"\u{1D517}",Therefore:"\u2234",Theta:"\u0398",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",Topf:"\u{1D54B}",TripleDot:"\u20DB",Tscr:"\u{1D4AF}",Tstrok:"\u0166",Uacut:"\xDA",Uacute:"\xDA",Uarr:"\u219F",Uarrocir:"\u2949",Ubrcy:"\u040E",Ubreve:"\u016C",Ucir:"\xDB",Ucirc:"\xDB",Ucy:"\u0423",Udblac:"\u0170",Ufr:"\u{1D518}",Ugrav:"\xD9",Ugrave:"\xD9",Umacr:"\u016A",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",Uopf:"\u{1D54C}",UpArrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21C5",UpDownArrow:"\u2195",UpEquilibrium:"\u296E",UpTee:"\u22A5",UpTeeArrow:"\u21A5",Uparrow:"\u21D1",Updownarrow:"\u21D5",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03D2",Upsilon:"\u03A5",Uring:"\u016E",Uscr:"\u{1D4B0}",Utilde:"\u0168",Uum:"\xDC",Uuml:"\xDC",VDash:"\u22AB",Vbar:"\u2AEB",Vcy:"\u0412",Vdash:"\u22A9",Vdashl:"\u2AE6",Vee:"\u22C1",Verbar:"\u2016",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",Vopf:"\u{1D54D}",Vscr:"\u{1D4B1}",Vvdash:"\u22AA",Wcirc:"\u0174",Wedge:"\u22C0",Wfr:"\u{1D51A}",Wopf:"\u{1D54E}",Wscr:"\u{1D4B2}",Xfr:"\u{1D51B}",Xi:"\u039E",Xopf:"\u{1D54F}",Xscr:"\u{1D4B3}",YAcy:"\u042F",YIcy:"\u0407",YUcy:"\u042E",Yacut:"\xDD",Yacute:"\xDD",Ycirc:"\u0176",Ycy:"\u042B",Yfr:"\u{1D51C}",Yopf:"\u{1D550}",Yscr:"\u{1D4B4}",Yuml:"\u0178",ZHcy:"\u0416",Zacute:"\u0179",Zcaron:"\u017D",Zcy:"\u0417",Zdot:"\u017B",ZeroWidthSpace:"\u200B",Zeta:"\u0396",Zfr:"\u2128",Zopf:"\u2124",Zscr:"\u{1D4B5}",aacut:"\xE1",aacute:"\xE1",abreve:"\u0103",ac:"\u223E",acE:"\u223E\u0333",acd:"\u223F",acir:"\xE2",acirc:"\xE2",acut:"\xB4",acute:"\xB4",acy:"\u0430",aeli:"\xE6",aelig:"\xE6",af:"\u2061",afr:"\u{1D51E}",agrav:"\xE0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",alpha:"\u03B1",amacr:"\u0101",amalg:"\u2A3F",am:"&",amp:"&",and:"\u2227",andand:"\u2A55",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",aogon:"\u0105",aopf:"\u{1D552}",ap:"\u2248",apE:"\u2A70",apacir:"\u2A6F",ape:"\u224A",apid:"\u224B",apos:"'",approx:"\u2248",approxeq:"\u224A",arin:"\xE5",aring:"\xE5",ascr:"\u{1D4B6}",ast:"*",asymp:"\u2248",asympeq:"\u224D",atild:"\xE3",atilde:"\xE3",aum:"\xE4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",bNot:"\u2AED",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",barvee:"\u22BD",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",beta:"\u03B2",beth:"\u2136",between:"\u226C",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bnot:"\u2310",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxDL:"\u2557",boxDR:"\u2554",boxDl:"\u2556",boxDr:"\u2553",boxH:"\u2550",boxHD:"\u2566",boxHU:"\u2569",boxHd:"\u2564",boxHu:"\u2567",boxUL:"\u255D",boxUR:"\u255A",boxUl:"\u255C",boxUr:"\u2559",boxV:"\u2551",boxVH:"\u256C",boxVL:"\u2563",boxVR:"\u2560",boxVh:"\u256B",boxVl:"\u2562",boxVr:"\u255F",boxbox:"\u29C9",boxdL:"\u2555",boxdR:"\u2552",boxdl:"\u2510",boxdr:"\u250C",boxh:"\u2500",boxhD:"\u2565",boxhU:"\u2568",boxhd:"\u252C",boxhu:"\u2534",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxuL:"\u255B",boxuR:"\u2558",boxul:"\u2518",boxur:"\u2514",boxv:"\u2502",boxvH:"\u256A",boxvL:"\u2561",boxvR:"\u255E",boxvh:"\u253C",boxvl:"\u2524",boxvr:"\u251C",bprime:"\u2035",breve:"\u02D8",brvba:"\xA6",brvbar:"\xA6",bscr:"\u{1D4B7}",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsol:"\\",bsolb:"\u29C5",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",bumpeq:"\u224F",cacute:"\u0107",cap:"\u2229",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",capcup:"\u2A47",capdot:"\u2A40",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",ccaps:"\u2A4D",ccaron:"\u010D",ccedi:"\xE7",ccedil:"\xE7",ccirc:"\u0109",ccups:"\u2A4C",ccupssm:"\u2A50",cdot:"\u010B",cedi:"\xB8",cedil:"\xB8",cemptyv:"\u29B2",cen:"\xA2",cent:"\xA2",centerdot:"\xB7",cfr:"\u{1D520}",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",chi:"\u03C7",cir:"\u25CB",cirE:"\u29C3",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledR:"\xAE",circledS:"\u24C8",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",clubs:"\u2663",clubsuit:"\u2663",colon:":",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",conint:"\u222E",copf:"\u{1D554}",coprod:"\u2210",cop:"\xA9",copy:"\xA9",copysr:"\u2117",crarr:"\u21B5",cross:"\u2717",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cup:"\u222A",cupbrcap:"\u2A48",cupcap:"\u2A46",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curre:"\xA4",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dArr:"\u21D3",dHar:"\u2965",dagger:"\u2020",daleth:"\u2138",darr:"\u2193",dash:"\u2010",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",dcaron:"\u010F",dcy:"\u0434",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21CA",ddotseq:"\u2A77",de:"\xB0",deg:"\xB0",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",dfr:"\u{1D521}",dharl:"\u21C3",dharr:"\u21C2",diam:"\u22C4",diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divid:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",dopf:"\u{1D555}",dot:"\u02D9",doteq:"\u2250",doteqdot:"\u2251",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",downarrow:"\u2193",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",dscr:"\u{1D4B9}",dscy:"\u0455",dsol:"\u29F6",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",dzcy:"\u045F",dzigrarr:"\u27FF",eDDot:"\u2A77",eDot:"\u2251",eacut:"\xE9",eacute:"\xE9",easter:"\u2A6E",ecaron:"\u011B",ecir:"\xEA",ecirc:"\xEA",ecolon:"\u2255",ecy:"\u044D",edot:"\u0117",ee:"\u2147",efDot:"\u2252",efr:"\u{1D522}",eg:"\u2A9A",egrav:"\xE8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",emptyv:"\u2205",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",eng:"\u014B",ensp:"\u2002",eogon:"\u0119",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",equals:"=",equest:"\u225F",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erDot:"\u2253",erarr:"\u2971",escr:"\u212F",esdot:"\u2250",esim:"\u2242",eta:"\u03B7",et:"\xF0",eth:"\xF0",eum:"\xEB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",expectation:"\u2130",exponentiale:"\u2147",fallingdotseq:"\u2252",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",ffr:"\u{1D523}",filig:"\uFB01",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",fopf:"\u{1D557}",forall:"\u2200",fork:"\u22D4",forkv:"\u2AD9",fpartint:"\u2A0D",frac1:"\xBC",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac3:"\xBE",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",gE:"\u2267",gEl:"\u2A8C",gacute:"\u01F5",gamma:"\u03B3",gammad:"\u03DD",gap:"\u2A86",gbreve:"\u011F",gcirc:"\u011D",gcy:"\u0433",gdot:"\u0121",ge:"\u2265",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",ges:"\u2A7E",gescc:"\u2AA9",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",gfr:"\u{1D524}",gg:"\u226B",ggg:"\u22D9",gimel:"\u2137",gjcy:"\u0453",gl:"\u2277",glE:"\u2A92",gla:"\u2AA5",glj:"\u2AA4",gnE:"\u2269",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",gopf:"\u{1D558}",grave:"`",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",g:">",gt:">",gtcc:"\u2AA7",gtcir:"\u2A7A",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",hArr:"\u21D4",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",hardcy:"\u044A",harr:"\u2194",harrcir:"\u2948",harrw:"\u21AD",hbar:"\u210F",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",horbar:"\u2015",hscr:"\u{1D4BD}",hslash:"\u210F",hstrok:"\u0127",hybull:"\u2043",hyphen:"\u2010",iacut:"\xED",iacute:"\xED",ic:"\u2063",icir:"\xEE",icirc:"\xEE",icy:"\u0438",iecy:"\u0435",iexc:"\xA1",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",igrav:"\xEC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",ijlig:"\u0133",imacr:"\u012B",image:"\u2111",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22B7",imped:"\u01B5",in:"\u2208",incare:"\u2105",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",int:"\u222B",intcal:"\u22BA",integers:"\u2124",intercal:"\u22BA",intlarhk:"\u2A17",intprod:"\u2A3C",iocy:"\u0451",iogon:"\u012F",iopf:"\u{1D55A}",iota:"\u03B9",iprod:"\u2A3C",iques:"\xBF",iquest:"\xBF",iscr:"\u{1D4BE}",isin:"\u2208",isinE:"\u22F9",isindot:"\u22F5",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",itilde:"\u0129",iukcy:"\u0456",ium:"\xEF",iuml:"\xEF",jcirc:"\u0135",jcy:"\u0439",jfr:"\u{1D527}",jmath:"\u0237",jopf:"\u{1D55B}",jscr:"\u{1D4BF}",jsercy:"\u0458",jukcy:"\u0454",kappa:"\u03BA",kappav:"\u03F0",kcedil:"\u0137",kcy:"\u043A",kfr:"\u{1D528}",kgreen:"\u0138",khcy:"\u0445",kjcy:"\u045C",kopf:"\u{1D55C}",kscr:"\u{1D4C0}",lAarr:"\u21DA",lArr:"\u21D0",lAtail:"\u291B",lBarr:"\u290E",lE:"\u2266",lEg:"\u2A8B",lHar:"\u2962",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",lambda:"\u03BB",lang:"\u27E8",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",laqu:"\xAB",laquo:"\xAB",larr:"\u2190",larrb:"\u21E4",larrbfs:"\u291F",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",lat:"\u2AAB",latail:"\u2919",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",lcaron:"\u013E",lcedil:"\u013C",lceil:"\u2308",lcub:"{",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",leftarrow:"\u2190",leftarrowtail:"\u21A2",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",leftthreetimes:"\u22CB",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",les:"\u2A7D",lescc:"\u2AA8",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",lessgtr:"\u2276",lesssim:"\u2272",lfisht:"\u297C",lfloor:"\u230A",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",ljcy:"\u0459",ll:"\u226A",llarr:"\u21C7",llcorner:"\u231E",llhard:"\u296B",lltri:"\u25FA",lmidot:"\u0140",lmoust:"\u23B0",lmoustache:"\u23B0",lnE:"\u2268",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",longleftrightarrow:"\u27F7",longmapsto:"\u27FC",longrightarrow:"\u27F6",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",lstrok:"\u0142",l:"<",lt:"<",ltcc:"\u2AA6",ltcir:"\u2A79",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltrPar:"\u2996",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",mDDot:"\u223A",mac:"\xAF",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",mcy:"\u043C",mdash:"\u2014",measuredangle:"\u2221",mfr:"\u{1D52A}",mho:"\u2127",micr:"\xB5",micro:"\xB5",mid:"\u2223",midast:"*",midcir:"\u2AF0",middo:"\xB7",middot:"\xB7",minus:"\u2212",minusb:"\u229F",minusd:"\u2238",minusdu:"\u2A2A",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",mstpos:"\u223E",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nGg:"\u22D9\u0338",nGt:"\u226B\u20D2",nGtv:"\u226B\u0338",nLeftarrow:"\u21CD",nLeftrightarrow:"\u21CE",nLl:"\u22D8\u0338",nLt:"\u226A\u20D2",nLtv:"\u226A\u0338",nRightarrow:"\u21CF",nVDash:"\u22AF",nVdash:"\u22AE",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266E",natural:"\u266E",naturals:"\u2115",nbs:"\xA0",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",ncy:"\u043D",ndash:"\u2013",ne:"\u2260",neArr:"\u21D7",nearhk:"\u2924",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",ngsim:"\u2275",ngt:"\u226F",ngtr:"\u226F",nhArr:"\u21CE",nharr:"\u21AE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",njcy:"\u045A",nlArr:"\u21CD",nlE:"\u2266\u0338",nlarr:"\u219A",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219A",nleftrightarrow:"\u21AE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nlsim:"\u2274",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nmid:"\u2224",nopf:"\u{1D55F}",no:"\xAC",not:"\xAC",notin:"\u2209",notinE:"\u22F9\u0338",notindot:"\u22F5\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",npre:"\u2AAF\u0338",nprec:"\u2280",npreceq:"\u2AAF\u0338",nrArr:"\u21CF",nrarr:"\u219B",nrarrc:"\u2933\u0338",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",ntild:"\xF1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvDash:"\u22AD",nvHarr:"\u2904",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwArr:"\u21D6",nwarhk:"\u2923",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",oS:"\u24C8",oacut:"\xF3",oacute:"\xF3",oast:"\u229B",ocir:"\xF4",ocirc:"\xF4",ocy:"\u043E",odash:"\u229D",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",oelig:"\u0153",ofcir:"\u29BF",ofr:"\u{1D52C}",ogon:"\u02DB",ograv:"\xF2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",omacr:"\u014D",omega:"\u03C9",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",oopf:"\u{1D560}",opar:"\u29B7",operp:"\u29B9",oplus:"\u2295",or:"\u2228",orarr:"\u21BB",ord:"\xBA",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oscr:"\u2134",oslas:"\xF8",oslash:"\xF8",osol:"\u2298",otild:"\xF5",otilde:"\xF5",otimes:"\u2297",otimesas:"\u2A36",oum:"\xF6",ouml:"\xF6",ovbar:"\u233D",par:"\xB6",para:"\xB6",parallel:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",pfr:"\u{1D52D}",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plus:"+",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",plusm:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",pointint:"\u2A15",popf:"\u{1D561}",poun:"\xA3",pound:"\xA3",pr:"\u227A",prE:"\u2AB3",prap:"\u2AB7",prcue:"\u227C",pre:"\u2AAF",prec:"\u227A",precapprox:"\u2AB7",preccurlyeq:"\u227C",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",precsim:"\u227E",prime:"\u2032",primes:"\u2119",prnE:"\u2AB5",prnap:"\u2AB9",prnsim:"\u22E8",prod:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",pscr:"\u{1D4C5}",psi:"\u03C8",puncsp:"\u2008",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",qprime:"\u2057",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quo:'"',quot:'"',rAarr:"\u21DB",rArr:"\u21D2",rAtail:"\u291C",rBarr:"\u290F",rHar:"\u2964",race:"\u223D\u0331",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raqu:"\xBB",raquo:"\xBB",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",rect:"\u25AD",re:"\xAE",reg:"\xAE",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",rho:"\u03C1",rhov:"\u03F1",rightarrow:"\u2192",rightarrowtail:"\u21A3",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",rightthreetimes:"\u22CC",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoust:"\u23B1",rmoustache:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",roplus:"\u2A2E",rotimes:"\u2A35",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",rsaquo:"\u203A",rscr:"\u{1D4C7}",rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",ruluhar:"\u2968",rx:"\u211E",sacute:"\u015B",sbquo:"\u201A",sc:"\u227B",scE:"\u2AB4",scap:"\u2AB8",scaron:"\u0161",sccue:"\u227D",sce:"\u2AB0",scedil:"\u015F",scirc:"\u015D",scnE:"\u2AB6",scnap:"\u2ABA",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",scy:"\u0441",sdot:"\u22C5",sdotb:"\u22A1",sdote:"\u2A66",seArr:"\u21D8",searhk:"\u2925",searr:"\u2198",searrow:"\u2198",sec:"\xA7",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",shchcy:"\u0449",shcy:"\u0448",shortmid:"\u2223",shortparallel:"\u2225",sh:"\xAD",shy:"\xAD",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",softcy:"\u044C",sol:"/",solb:"\u29C4",solbar:"\u233F",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25A1",square:"\u25A1",squarf:"\u25AA",squf:"\u25AA",srarr:"\u2192",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",subE:"\u2AC5",subdot:"\u2ABD",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2AC5",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succ:"\u227B",succapprox:"\u2AB8",succcurlyeq:"\u227D",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",sum:"\u2211",sung:"\u266A",sup:"\u2283",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",supE:"\u2AC6",supdot:"\u2ABE",supdsub:"\u2AD8",supe:"\u2287",supedot:"\u2AC4",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swArr:"\u21D9",swarhk:"\u2926",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292A",szli:"\xDF",szlig:"\xDF",target:"\u2316",tau:"\u03C4",tbrk:"\u23B4",tcaron:"\u0165",tcedil:"\u0163",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",thor:"\xFE",thorn:"\xFE",tilde:"\u02DC",time:"\xD7",times:"\xD7",timesb:"\u22A0",timesbar:"\u2A31",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",top:"\u22A4",topbot:"\u2336",topcir:"\u2AF1",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",tscr:"\u{1D4C9}",tscy:"\u0446",tshcy:"\u045B",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",uArr:"\u21D1",uHar:"\u2963",uacut:"\xFA",uacute:"\xFA",uarr:"\u2191",ubrcy:"\u045E",ubreve:"\u016D",ucir:"\xFB",ucirc:"\xFB",ucy:"\u0443",udarr:"\u21C5",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",ufr:"\u{1D532}",ugrav:"\xF9",ugrave:"\xF9",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",umacr:"\u016B",um:"\xA8",uml:"\xA8",uogon:"\u0173",uopf:"\u{1D566}",uparrow:"\u2191",updownarrow:"\u2195",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",upsi:"\u03C5",upsih:"\u03D2",upsilon:"\u03C5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",uring:"\u016F",urtri:"\u25F9",uscr:"\u{1D4CA}",utdot:"\u22F0",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",uum:"\xFC",uuml:"\xFC",uwangle:"\u29A7",vArr:"\u21D5",vBar:"\u2AE8",vBarv:"\u2AE9",vDash:"\u22A8",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vcy:"\u0432",vdash:"\u22A2",vee:"\u2228",veebar:"\u22BB",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",vert:"|",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",vzigzag:"\u299A",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",wfr:"\u{1D534}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",xfr:"\u{1D535}",xhArr:"\u27FA",xharr:"\u27F7",xi:"\u03BE",xlArr:"\u27F8",xlarr:"\u27F5",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrArr:"\u27F9",xrarr:"\u27F6",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",yacut:"\xFD",yacute:"\xFD",yacy:"\u044F",ycirc:"\u0177",ycy:"\u044B",ye:"\xA5",yen:"\xA5",yfr:"\u{1D536}",yicy:"\u0457",yopf:"\u{1D56A}",yscr:"\u{1D4CE}",yucy:"\u044E",yum:"\xFF",yuml:"\xFF",zacute:"\u017A",zcaron:"\u017E",zcy:"\u0437",zdot:"\u017C",zeetrf:"\u2128",zeta:"\u03B6",zfr:"\u{1D537}",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}});var zu=C((kv,Uu)=>{"use strict";var Mu=Nu();Uu.exports=_D;var qD={}.hasOwnProperty;function _D(e){return qD.call(Mu,e)?Mu[e]:!1}});var dr=C((Bv,ea)=>{"use strict";var Gu=qu(),Yu=_u(),SD=Re(),PD=Ou(),Wu=Ru(),OD=zu();ea.exports=WD;var LD={}.hasOwnProperty,Ke=String.fromCharCode,ID=Function.prototype,$u={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},RD=9,Vu=10,ND=12,MD=32,ju=38,UD=59,zD=60,GD=61,YD=35,$D=88,VD=120,jD=65533,Xe="named",zt="hexadecimal",Gt="decimal",Yt={};Yt[zt]=16;Yt[Gt]=10;var jr={};jr[Xe]=Wu;jr[Gt]=SD;jr[zt]=PD;var Hu=1,Ku=2,Xu=3,Ju=4,Qu=5,Ut=6,Zu=7,we={};we[Hu]="Named character references must be terminated by a semicolon";we[Ku]="Numeric character references must be terminated by a semicolon";we[Xu]="Named character references cannot be empty";we[Ju]="Numeric character references cannot be empty";we[Qu]="Named character references must be known";we[Ut]="Numeric character references cannot be disallowed";we[Zu]="Numeric character references cannot be outside the permissible Unicode range";function WD(e,r){var t={},n,a;r||(r={});for(a in $u)n=r[a],t[a]=n??$u[a];return(t.position.indent||t.position.start)&&(t.indent=t.position.indent||[],t.position=t.position.start),HD(e,t)}function HD(e,r){var t=r.additional,n=r.nonTerminated,a=r.text,i=r.reference,u=r.warning,o=r.textContext,s=r.referenceContext,l=r.warningContext,c=r.position,f=r.indent||[],p=e.length,d=0,D=-1,h=c.column||1,m=c.line||1,F="",A=[],v,B,b,g,y,w,E,x,k,T,q,N,P,S,_,O,Be,W,I;for(typeof t=="string"&&(t=t.charCodeAt(0)),O=ee(),x=u?Z:ID,d--,p++;++d<p;)if(y===Vu&&(h=f[D]||1),y=e.charCodeAt(d),y===ju){if(E=e.charCodeAt(d+1),E===RD||E===Vu||E===ND||E===MD||E===ju||E===zD||E!==E||t&&E===t){F+=Ke(y),h++;continue}for(P=d+1,N=P,I=P,E===YD?(I=++N,E=e.charCodeAt(I),E===$D||E===VD?(S=zt,I=++N):S=Gt):S=Xe,v="",q="",g="",_=jr[S],I--;++I<p&&(E=e.charCodeAt(I),!!_(E));)g+=Ke(E),S===Xe&&LD.call(Gu,g)&&(v=g,q=Gu[g]);b=e.charCodeAt(I)===UD,b&&(I++,B=S===Xe?OD(g):!1,B&&(v=g,q=B)),W=1+I-P,!b&&!n||(g?S===Xe?(b&&!q?x(Qu,1):(v!==g&&(I=N+v.length,W=1+I-N,b=!1),b||(k=v?Hu:Xu,r.attribute?(E=e.charCodeAt(I),E===GD?(x(k,W),q=null):Wu(E)?q=null:x(k,W)):x(k,W))),w=q):(b||x(Ku,W),w=parseInt(g,Yt[S]),KD(w)?(x(Zu,W),w=Ke(jD)):w in Yu?(x(Ut,W),w=Yu[w]):(T="",XD(w)&&x(Ut,W),w>65535&&(w-=65536,T+=Ke(w>>>10|55296),w=56320|w&1023),w=T+Ke(w))):S!==Xe&&x(Ju,W)),w?(Ee(),O=ee(),d=I-1,h+=I-P+1,A.push(w),Be=ee(),Be.offset++,i&&i.call(s,w,{start:O,end:Be},e.slice(P-1,I)),O=Be):(g=e.slice(P-1,I),F+=g,h+=g.length,d=I-1)}else y===10&&(m++,D++,h=0),y===y?(F+=Ke(y),h++):Ee();return A.join("");function ee(){return{line:m,column:h,offset:d+(c.offset||0)}}function Z(ve,U){var ht=ee();ht.column+=U,ht.offset+=U,u.call(l,we[ve],ht,ve)}function Ee(){F&&(A.push(F),a&&a.call(o,F,{start:O,end:ee()}),F="")}}function KD(e){return e>=55296&&e<=57343||e>1114111}function XD(e){return e>=1&&e<=8||e===11||e>=13&&e<=31||e>=127&&e<=159||e>=64976&&e<=65007||(e&65535)===65535||(e&65535)===65534}});var na=C((Tv,ta)=>{"use strict";var JD=Ie(),ra=dr();ta.exports=QD;function QD(e){return t.raw=n,t;function r(i){for(var u=e.offset,o=i.line,s=[];++o&&o in u;)s.push((u[o]||0)+1);return{start:i,indent:s}}function t(i,u,o){ra(i,{position:r(u),warning:a,text:o,reference:o,textContext:e,referenceContext:e})}function n(i,u,o){return ra(i,JD(o,{position:r(u),warning:a}))}function a(i,u,o){o!==3&&e.file.message(i,u)}}});var aa=C((qv,ua)=>{"use strict";ua.exports=ZD;function ZD(e){return r;function r(t,n){var a=this,i=a.offset,u=[],o=a[e+"Methods"],s=a[e+"Tokenizers"],l=n.line,c=n.column,f,p,d,D,h,m;if(!t)return u;for(w.now=v,w.file=a.file,F("");t;){for(f=-1,p=o.length,h=!1;++f<p&&(D=o[f],d=s[D],!(d&&(!d.onlyAtStart||a.atStart)&&(!d.notInList||!a.inList)&&(!d.notInBlock||!a.inBlock)&&(!d.notInLink||!a.inLink)&&(m=t.length,d.apply(a,[w,t]),h=m!==t.length,h))););h||a.file.fail(new Error("Infinite loop"),w.now())}return a.eof=v(),u;function F(E){for(var x=-1,k=E.indexOf(`
`);k!==-1;)l++,x=k,k=E.indexOf(`
`,k+1);x===-1?c+=E.length:c=E.length-x,l in i&&(x!==-1?c+=i[l]:c<=i[l]&&(c=i[l]+1))}function A(){var E=[],x=l+1;return function(){for(var k=l+1;x<k;)E.push((i[x]||0)+1),x++;return E}}function v(){var E={line:l,column:c};return E.offset=a.toOffset(E),E}function B(E){this.start=E,this.end=v()}function b(E){t.slice(0,E.length)!==E&&a.file.fail(new Error("Incorrectly eaten value: please report this warning on https://git.io/vg5Ft"),v())}function g(){var E=v();return x;function x(k,T){var q=k.position,N=q?q.start:E,P=[],S=q&&q.end.line,_=E.line;if(k.position=new B(N),q&&T&&q.indent){if(P=q.indent,S<_){for(;++S<_;)P.push((i[S]||0)+1);P.push(E.column)}T=P.concat(T)}return k.position.indent=T||[],k}}function y(E,x){var k=x?x.children:u,T=k[k.length-1],q;return T&&E.type===T.type&&(E.type==="text"||E.type==="blockquote")&&ia(T)&&ia(E)&&(q=E.type==="text"?ep:rp,E=q.call(a,T,E)),E!==T&&k.push(E),a.atStart&&u.length!==0&&a.exitStart(),E}function w(E){var x=A(),k=g(),T=v();return b(E),q.reset=N,N.test=P,q.test=P,t=t.slice(E.length),F(E),x=x(),q;function q(S,_){return k(y(k(S),_),x)}function N(){var S=q.apply(null,arguments);return l=T.line,c=T.column,t=E+t,S}function P(){var S=k({});return l=T.line,c=T.column,t=E+t,S.position}}}}function ia(e){var r,t;return e.type!=="text"||!e.position?!0:(r=e.position.start,t=e.position.end,r.line!==t.line||t.column-r.column===e.value.length)}function ep(e,r){return e.value+=r.value,e}function rp(e,r){return this.options.commonmark||this.options.gfm?r:(e.children=e.children.concat(r.children),e)}});var ca=C((_v,sa)=>{"use strict";sa.exports=Wr;var $t=["\\","`","*","{","}","[","]","(",")","#","+","-",".","!","_",">"],Vt=$t.concat(["~","|"]),oa=Vt.concat([`
`,'"',"$","%","&","'",",","/",":",";","<","=","?","@","^"]);Wr.default=$t;Wr.gfm=Vt;Wr.commonmark=oa;function Wr(e){var r=e||{};return r.commonmark?oa:r.gfm?Vt:$t}});var fa=C((Sv,la)=>{"use strict";la.exports=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","legend","li","link","main","menu","menuitem","meta","nav","noframes","ol","optgroup","option","p","param","pre","section","source","title","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]});var jt=C((Pv,Da)=>{"use strict";Da.exports={position:!0,gfm:!0,commonmark:!1,pedantic:!1,blocks:fa()}});var ha=C((Ov,pa)=>{"use strict";var tp=Ie(),np=ca(),ip=jt();pa.exports=up;function up(e){var r=this,t=r.options,n,a;if(e==null)e={};else if(typeof e=="object")e=tp(e);else throw new Error("Invalid value `"+e+"` for setting `options`");for(n in ip){if(a=e[n],a==null&&(a=t[n]),n!=="blocks"&&typeof a!="boolean"||n==="blocks"&&typeof a!="object")throw new Error("Invalid value `"+a+"` for setting `options."+n+"`");e[n]=a}return r.options=e,r.escape=np(e),r}});var Fa=C((Lv,ma)=>{"use strict";ma.exports=da;function da(e){if(e==null)return cp;if(typeof e=="string")return sp(e);if(typeof e=="object")return"length"in e?op(e):ap(e);if(typeof e=="function")return e;throw new Error("Expected function, string, or object as test")}function ap(e){return r;function r(t){var n;for(n in e)if(t[n]!==e[n])return!1;return!0}}function op(e){for(var r=[],t=-1;++t<e.length;)r[t]=da(e[t]);return n;function n(){for(var a=-1;++a<r.length;)if(r[a].apply(this,arguments))return!0;return!1}}function sp(e){return r;function r(t){return!!(t&&t.type===e)}}function cp(){return!0}});var Ea=C((Iv,ga)=>{ga.exports=lp;function lp(e){return e}});var Aa=C((Rv,ba)=>{"use strict";ba.exports=Hr;var fp=Fa(),Dp=Ea(),va=!0,Ca="skip",Wt=!1;Hr.CONTINUE=va;Hr.SKIP=Ca;Hr.EXIT=Wt;function Hr(e,r,t,n){var a,i;typeof r=="function"&&typeof t!="function"&&(n=t,t=r,r=null),i=fp(r),a=n?-1:1,u(e,null,[])();function u(o,s,l){var c=typeof o=="object"&&o!==null?o:{},f;return typeof c.type=="string"&&(f=typeof c.tagName=="string"?c.tagName:typeof c.name=="string"?c.name:void 0,p.displayName="node ("+Dp(c.type+(f?"<"+f+">":""))+")"),p;function p(){var d=l.concat(o),D=[],h,m;if((!r||i(o,s,l[l.length-1]||null))&&(D=pp(t(o,l)),D[0]===Wt))return D;if(o.children&&D[0]!==Ca)for(m=(n?o.children.length:-1)+a;m>-1&&m<o.children.length;){if(h=u(o.children[m],m,d)(),h[0]===Wt)return h;m=typeof h[1]=="number"?h[1]:m+a}return D}}}function pp(e){return e!==null&&typeof e=="object"&&"length"in e?e:typeof e=="number"?[va,e]:[e]}});var wa=C((Nv,ya)=>{"use strict";ya.exports=Xr;var Kr=Aa(),hp=Kr.CONTINUE,dp=Kr.SKIP,mp=Kr.EXIT;Xr.CONTINUE=hp;Xr.SKIP=dp;Xr.EXIT=mp;function Xr(e,r,t,n){typeof r=="function"&&typeof t!="function"&&(n=t,t=r,r=null),Kr(e,r,a,n);function a(i,u){var o=u[u.length-1],s=o?o.children.indexOf(i):null;return t(i,s,o)}}});var ka=C((Mv,xa)=>{"use strict";var Fp=wa();xa.exports=gp;function gp(e,r){return Fp(e,r?Ep:vp),e}function Ep(e){delete e.position}function vp(e){e.position=void 0}});var qa=C((Uv,Ta)=>{"use strict";var Ba=Ie(),Cp=ka();Ta.exports=yp;var bp=`
`,Ap=/\r\n|\r/g;function yp(){var e=this,r=String(e.file),t={line:1,column:1,offset:0},n=Ba(t),a;return r=r.replace(Ap,bp),r.charCodeAt(0)===65279&&(r=r.slice(1),n.column++,n.offset++),a={type:"root",children:e.tokenizeBlock(r,n),position:{start:t,end:e.eof||Ba(t)}},e.options.position||Cp(a,!0),a}});var Sa=C((zv,_a)=>{"use strict";var wp=/^[ \t]*(\n|$)/;_a.exports=xp;function xp(e,r,t){for(var n,a="",i=0,u=r.length;i<u&&(n=wp.exec(r.slice(i)),n!=null);)i+=n[0].length,a+=n[0];if(a!==""){if(t)return!0;e(a)}}});var Jr=C((Gv,Pa)=>{"use strict";var me="",Ht;Pa.exports=kp;function kp(e,r){if(typeof e!="string")throw new TypeError("expected a string");if(r===1)return e;if(r===2)return e+e;var t=e.length*r;if(Ht!==e||typeof Ht>"u")Ht=e,me="";else if(me.length>=t)return me.substr(0,t);for(;t>me.length&&r>1;)r&1&&(me+=e),r>>=1,e+=e;return me+=e,me=me.substr(0,t),me}});var Kt=C((Yv,Oa)=>{"use strict";Oa.exports=Bp;function Bp(e){return String(e).replace(/\n+$/,"")}});var Ra=C(($v,Ia)=>{"use strict";var Tp=Jr(),qp=Kt();Ia.exports=Pp;var Xt=`
`,La="	",Jt=" ",_p=4,Sp=Tp(Jt,_p);function Pp(e,r,t){for(var n=-1,a=r.length,i="",u="",o="",s="",l,c,f;++n<a;)if(l=r.charAt(n),f)if(f=!1,i+=o,u+=s,o="",s="",l===Xt)o=l,s=l;else for(i+=l,u+=l;++n<a;){if(l=r.charAt(n),!l||l===Xt){s=l,o=l;break}i+=l,u+=l}else if(l===Jt&&r.charAt(n+1)===l&&r.charAt(n+2)===l&&r.charAt(n+3)===l)o+=Sp,n+=3,f=!0;else if(l===La)o+=l,f=!0;else{for(c="";l===La||l===Jt;)c+=l,l=r.charAt(++n);if(l!==Xt)break;o+=c+l,s+=l}if(u)return t?!0:e(i)({type:"code",lang:null,meta:null,value:qp(u)})}});var Ua=C((Vv,Ma)=>{"use strict";Ma.exports=Rp;var Qr=`
`,mr="	",Je=" ",Op="~",Na="`",Lp=3,Ip=4;function Rp(e,r,t){var n=this,a=n.options.gfm,i=r.length+1,u=0,o="",s,l,c,f,p,d,D,h,m,F,A,v,B;if(a){for(;u<i&&(c=r.charAt(u),!(c!==Je&&c!==mr));)o+=c,u++;if(v=u,c=r.charAt(u),!(c!==Op&&c!==Na)){for(u++,l=c,s=1,o+=c;u<i&&(c=r.charAt(u),c===l);)o+=c,s++,u++;if(!(s<Lp)){for(;u<i&&(c=r.charAt(u),!(c!==Je&&c!==mr));)o+=c,u++;for(f="",D="";u<i&&(c=r.charAt(u),!(c===Qr||l===Na&&c===l));)c===Je||c===mr?D+=c:(f+=D+c,D=""),u++;if(c=r.charAt(u),!(c&&c!==Qr)){if(t)return!0;B=e.now(),B.column+=o.length,B.offset+=o.length,o+=f,f=n.decode.raw(n.unescape(f),B),D&&(o+=D),D="",F="",A="",h="",m="";for(var b=!0;u<i;){if(c=r.charAt(u),h+=F,m+=A,F="",A="",c!==Qr){h+=c,A+=c,u++;continue}for(b?(o+=c,b=!1):(F+=c,A+=c),D="",u++;u<i&&(c=r.charAt(u),c===Je);)D+=c,u++;if(F+=D,A+=D.slice(v),!(D.length>=Ip)){for(D="";u<i&&(c=r.charAt(u),c===l);)D+=c,u++;if(F+=D,A+=D,!(D.length<s)){for(D="";u<i&&(c=r.charAt(u),!(c!==Je&&c!==mr));)F+=c,A+=c,u++;if(!c||c===Qr)break}}}for(o+=h+F,u=-1,i=f.length;++u<i;)if(c=f.charAt(u),c===Je||c===mr)p||(p=f.slice(0,u));else if(p){d=f.slice(u);break}return e(o)({type:"code",lang:p||f||null,meta:d||null,value:m})}}}}}});var Ne=C((Qe,za)=>{Qe=za.exports=Np;function Np(e){return e.trim?e.trim():Qe.right(Qe.left(e))}Qe.left=function(e){return e.trimLeft?e.trimLeft():e.replace(/^\s\s*/,"")};Qe.right=function(e){if(e.trimRight)return e.trimRight();for(var r=/\s/,t=e.length;r.test(e.charAt(--t)););return e.slice(0,t+1)}});var Zr=C((jv,Ga)=>{"use strict";Ga.exports=Mp;function Mp(e,r,t,n){for(var a=e.length,i=-1,u,o;++i<a;)if(u=e[i],o=u[1]||{},!(o.pedantic!==void 0&&o.pedantic!==t.options.pedantic)&&!(o.commonmark!==void 0&&o.commonmark!==t.options.commonmark)&&r[u[0]].apply(t,n))return!0;return!1}});var ja=C((Wv,Va)=>{"use strict";var Up=Ne(),zp=Zr();Va.exports=Gp;var Qt=`
`,Ya="	",Zt=" ",$a=">";function Gp(e,r,t){for(var n=this,a=n.offset,i=n.blockTokenizers,u=n.interruptBlockquote,o=e.now(),s=o.line,l=r.length,c=[],f=[],p=[],d,D=0,h,m,F,A,v,B,b,g;D<l&&(h=r.charAt(D),!(h!==Zt&&h!==Ya));)D++;if(r.charAt(D)===$a){if(t)return!0;for(D=0;D<l;){for(F=r.indexOf(Qt,D),B=D,b=!1,F===-1&&(F=l);D<l&&(h=r.charAt(D),!(h!==Zt&&h!==Ya));)D++;if(r.charAt(D)===$a?(D++,b=!0,r.charAt(D)===Zt&&D++):D=B,A=r.slice(D,F),!b&&!Up(A)){D=B;break}if(!b&&(m=r.slice(D),zp(u,i,n,[e,m,!0])))break;v=B===D?A:r.slice(B,F),p.push(D-B),c.push(v),f.push(A),D=F+1}for(D=-1,l=p.length,d=e(c.join(Qt));++D<l;)a[s]=(a[s]||0)+p[D],s++;return g=n.enterBlock(),f=n.tokenizeBlock(f.join(Qt),o),g(),d({type:"blockquote",children:f})}}});var Ka=C((Hv,Ha)=>{"use strict";Ha.exports=$p;var Wa=`
`,Fr="	",gr=" ",Er="#",Yp=6;function $p(e,r,t){for(var n=this,a=n.options.pedantic,i=r.length+1,u=-1,o=e.now(),s="",l="",c,f,p;++u<i;){if(c=r.charAt(u),c!==gr&&c!==Fr){u--;break}s+=c}for(p=0;++u<=i;){if(c=r.charAt(u),c!==Er){u--;break}s+=c,p++}if(!(p>Yp)&&!(!p||!a&&r.charAt(u+1)===Er)){for(i=r.length+1,f="";++u<i;){if(c=r.charAt(u),c!==gr&&c!==Fr){u--;break}f+=c}if(!(!a&&f.length===0&&c&&c!==Wa)){if(t)return!0;for(s+=f,f="",l="";++u<i&&(c=r.charAt(u),!(!c||c===Wa));){if(c!==gr&&c!==Fr&&c!==Er){l+=f+c,f="";continue}for(;c===gr||c===Fr;)f+=c,c=r.charAt(++u);if(!a&&l&&!f&&c===Er){l+=c;continue}for(;c===Er;)f+=c,c=r.charAt(++u);for(;c===gr||c===Fr;)f+=c,c=r.charAt(++u);u--}return o.column+=s.length,o.offset+=s.length,s+=l+f,e(s)({type:"heading",depth:p,children:n.tokenizeInline(l,o)})}}}});var Qa=C((Kv,Ja)=>{"use strict";Ja.exports=Jp;var Vp="	",jp=`
`,Xa=" ",Wp="*",Hp="-",Kp="_",Xp=3;function Jp(e,r,t){for(var n=-1,a=r.length+1,i="",u,o,s,l;++n<a&&(u=r.charAt(n),!(u!==Vp&&u!==Xa));)i+=u;if(!(u!==Wp&&u!==Hp&&u!==Kp))for(o=u,i+=u,s=1,l="";++n<a;)if(u=r.charAt(n),u===o)s++,i+=l+o,l="";else if(u===Xa)l+=u;else return s>=Xp&&(!u||u===jp)?(i+=l,t?!0:e(i)({type:"thematicBreak"})):void 0}});var en=C((Xv,eo)=>{"use strict";eo.exports=rh;var Za="	",Qp=" ",Zp=1,eh=4;function rh(e){for(var r=0,t=0,n=e.charAt(r),a={},i,u=0;n===Za||n===Qp;){for(i=n===Za?eh:Zp,t+=i,i>1&&(t=Math.floor(t/i)*i);u<t;)a[++u]=r;n=e.charAt(++r)}return{indent:t,stops:a}}});var no=C((Jv,to)=>{"use strict";var th=Ne(),nh=Jr(),ih=en();to.exports=oh;var ro=`
`,uh=" ",ah="!";function oh(e,r){var t=e.split(ro),n=t.length+1,a=1/0,i=[],u,o,s;for(t.unshift(nh(uh,r)+ah);n--;)if(o=ih(t[n]),i[n]=o.stops,th(t[n]).length!==0)if(o.indent)o.indent>0&&o.indent<a&&(a=o.indent);else{a=1/0;break}if(a!==1/0)for(n=t.length;n--;){for(s=i[n],u=a;u&&!(u in s);)u--;t[n]=t[n].slice(s[u]+1)}return t.shift(),t.join(ro)}});var co=C((Qv,so)=>{"use strict";var sh=Ne(),ch=Jr(),io=Re(),lh=en(),fh=no(),Dh=Zr();so.exports=vh;var rn="*",ph="_",uo="+",tn="-",ao=".",Fe=" ",ae=`
`,et="	",oo=")",hh="x",xe=4,dh=/\n\n(?!\s*$)/,mh=/^\[([ X\tx])][ \t]/,Fh=/^([ \t]*)([*+-]|\d+[.)])( {1,4}(?! )| |\t|$|(?=\n))([^\n]*)/,gh=/^([ \t]*)([*+-]|\d+[.)])([ \t]+)/,Eh=/^( {1,4}|\t)?/gm;function vh(e,r,t){for(var n=this,a=n.options.commonmark,i=n.options.pedantic,u=n.blockTokenizers,o=n.interruptList,s=0,l=r.length,c=null,f,p,d,D,h,m,F,A,v,B,b,g,y,w,E,x,k,T,q,N=!1,P,S,_,O;s<l&&(D=r.charAt(s),!(D!==et&&D!==Fe));)s++;if(D=r.charAt(s),D===rn||D===uo||D===tn)h=D,d=!1;else{for(d=!0,p="";s<l&&(D=r.charAt(s),!!io(D));)p+=D,s++;if(D=r.charAt(s),!p||!(D===ao||a&&D===oo)||t&&p!=="1")return;c=parseInt(p,10),h=D}if(D=r.charAt(++s),!(D!==Fe&&D!==et&&(i||D!==ae&&D!==""))){if(t)return!0;for(s=0,w=[],E=[],x=[];s<l;){for(m=r.indexOf(ae,s),F=s,A=!1,O=!1,m===-1&&(m=l),f=0;s<l;){if(D=r.charAt(s),D===et)f+=xe-f%xe;else if(D===Fe)f++;else break;s++}if(k&&f>=k.indent&&(O=!0),D=r.charAt(s),v=null,!O){if(D===rn||D===uo||D===tn)v=D,s++,f++;else{for(p="";s<l&&(D=r.charAt(s),!!io(D));)p+=D,s++;D=r.charAt(s),s++,p&&(D===ao||a&&D===oo)&&(v=D,f+=p.length+1)}if(v)if(D=r.charAt(s),D===et)f+=xe-f%xe,s++;else if(D===Fe){for(_=s+xe;s<_&&r.charAt(s)===Fe;)s++,f++;s===_&&r.charAt(s)===Fe&&(s-=xe-1,f-=xe-1)}else D!==ae&&D!==""&&(v=null)}if(v){if(!i&&h!==v)break;A=!0}else!a&&!O&&r.charAt(F)===Fe?O=!0:a&&k&&(O=f>=k.indent||f>xe),A=!1,s=F;if(b=r.slice(F,m),B=F===s?b:r.slice(s,m),(v===rn||v===ph||v===tn)&&u.thematicBreak.call(n,e,b,!0))break;if(g=y,y=!A&&!sh(B).length,O&&k)k.value=k.value.concat(x,b),E=E.concat(x,b),x=[];else if(A)x.length!==0&&(N=!0,k.value.push(""),k.trail=x.concat()),k={value:[b],indent:f,trail:[]},w.push(k),E=E.concat(x,b),x=[];else if(y){if(g&&!a)break;x.push(b)}else{if(g||Dh(o,u,n,[e,b,!0]))break;k.value=k.value.concat(x,b),E=E.concat(x,b),x=[]}s=m+1}for(P=e(E.join(ae)).reset({type:"list",ordered:d,start:c,spread:N,children:[]}),T=n.enterList(),q=n.enterBlock(),s=-1,l=w.length;++s<l;)k=w[s].value.join(ae),S=e.now(),e(k)(Ch(n,k,S),P),k=w[s].trail.join(ae),s!==l-1&&(k+=ae),e(k);return T(),q(),P}}function Ch(e,r,t){var n=e.offset,a=e.options.pedantic?bh:Ah,i=null,u,o;return r=a.apply(null,arguments),e.options.gfm&&(u=r.match(mh),u&&(o=u[0].length,i=u[1].toLowerCase()===hh,n[t.line]+=o,r=r.slice(o))),{type:"listItem",spread:dh.test(r),checked:i,children:e.tokenizeBlock(r,t)}}function bh(e,r,t){var n=e.offset,a=t.line;return r=r.replace(gh,i),a=t.line,r.replace(Eh,i);function i(u){return n[a]=(n[a]||0)+u.length,a++,""}}function Ah(e,r,t){var n=e.offset,a=t.line,i,u,o,s,l,c,f;for(r=r.replace(Fh,p),s=r.split(ae),l=fh(r,lh(i).indent).split(ae),l[0]=o,n[a]=(n[a]||0)+u.length,a++,c=0,f=s.length;++c<f;)n[a]=(n[a]||0)+s[c].length-l[c].length,a++;return l.join(ae);function p(d,D,h,m,F){return u=D+h+m,o=F,Number(h)<10&&u.length%2===1&&(h=Fe+h),i=D+ch(Fe,h.length)+m,i+o}}});var po=C((Zv,Do)=>{"use strict";Do.exports=Th;var nn=`
`,yh="	",lo=" ",fo="=",wh="-",xh=3,kh=1,Bh=2;function Th(e,r,t){for(var n=this,a=e.now(),i=r.length,u=-1,o="",s,l,c,f,p;++u<i;){if(c=r.charAt(u),c!==lo||u>=xh){u--;break}o+=c}for(s="",l="";++u<i;){if(c=r.charAt(u),c===nn){u--;break}c===lo||c===yh?l+=c:(s+=l+c,l="")}if(a.column+=o.length,a.offset+=o.length,o+=s+l,c=r.charAt(++u),f=r.charAt(++u),!(c!==nn||f!==fo&&f!==wh)){for(o+=c,l=f,p=f===fo?kh:Bh;++u<i;){if(c=r.charAt(u),c!==f){if(c!==nn)return;u--;break}l+=c}return t?!0:e(o+l)({type:"heading",depth:p,children:n.tokenizeInline(s,a)})}}});var an=C(un=>{"use strict";var qh="[a-zA-Z_:][a-zA-Z0-9:._-]*",_h="[^\"'=<>`\\u0000-\\u0020]+",Sh="'[^']*'",Ph='"[^"]*"',Oh="(?:"+_h+"|"+Sh+"|"+Ph+")",Lh="(?:\\s+"+qh+"(?:\\s*=\\s*"+Oh+")?)",ho="<[A-Za-z][A-Za-z0-9\\-]*"+Lh+"*\\s*\\/?>",mo="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Ih="<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->",Rh="<[?].*?[?]>",Nh="<![A-Za-z]+\\s+[^>]*>",Mh="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>";un.openCloseTag=new RegExp("^(?:"+ho+"|"+mo+")");un.tag=new RegExp("^(?:"+ho+"|"+mo+"|"+Ih+"|"+Rh+"|"+Nh+"|"+Mh+")")});var vo=C((rC,Eo)=>{"use strict";var Uh=an().openCloseTag;Eo.exports=rd;var zh="	",Gh=" ",Fo=`
`,Yh="<",$h=/^<(script|pre|style)(?=(\s|>|$))/i,Vh=/<\/(script|pre|style)>/i,jh=/^<!--/,Wh=/-->/,Hh=/^<\?/,Kh=/\?>/,Xh=/^<![A-Za-z]/,Jh=/>/,Qh=/^<!\[CDATA\[/,Zh=/]]>/,go=/^$/,ed=new RegExp(Uh.source+"\\s*$");function rd(e,r,t){for(var n=this,a=n.options.blocks.join("|"),i=new RegExp("^</?("+a+")(?=(\\s|/?>|$))","i"),u=r.length,o=0,s,l,c,f,p,d,D,h=[[$h,Vh,!0],[jh,Wh,!0],[Hh,Kh,!0],[Xh,Jh,!0],[Qh,Zh,!0],[i,go,!0],[ed,go,!1]];o<u&&(f=r.charAt(o),!(f!==zh&&f!==Gh));)o++;if(r.charAt(o)===Yh){for(s=r.indexOf(Fo,o+1),s=s===-1?u:s,l=r.slice(o,s),c=-1,p=h.length;++c<p;)if(h[c][0].test(l)){d=h[c];break}if(d){if(t)return d[2];if(o=s,!d[1].test(l))for(;o<u;){if(s=r.indexOf(Fo,o+1),s=s===-1?u:s,l=r.slice(o+1,s),d[1].test(l)){l&&(o=s);break}o=s}return D=r.slice(0,o),e(D)({type:"html",value:D})}}}});var oe=C((tC,Co)=>{"use strict";Co.exports=id;var td=String.fromCharCode,nd=/\s/;function id(e){return nd.test(typeof e=="number"?td(e):e.charAt(0))}});var on=C((nC,bo)=>{"use strict";var ud=Br();bo.exports=ad;function ad(e){return ud(e).toLowerCase()}});var To=C((iC,Bo)=>{"use strict";var od=oe(),sd=on();Bo.exports=Dd;var Ao='"',yo="'",cd="\\",Ze=`
`,rt="	",tt=" ",cn="[",vr="]",ld="(",fd=")",wo=":",xo="<",ko=">";function Dd(e,r,t){for(var n=this,a=n.options.commonmark,i=0,u=r.length,o="",s,l,c,f,p,d,D,h;i<u&&(f=r.charAt(i),!(f!==tt&&f!==rt));)o+=f,i++;if(f=r.charAt(i),f===cn){for(i++,o+=f,c="";i<u&&(f=r.charAt(i),f!==vr);)f===cd&&(c+=f,i++,f=r.charAt(i)),c+=f,i++;if(!(!c||r.charAt(i)!==vr||r.charAt(i+1)!==wo)){for(d=c,o+=c+vr+wo,i=o.length,c="";i<u&&(f=r.charAt(i),!(f!==rt&&f!==tt&&f!==Ze));)o+=f,i++;if(f=r.charAt(i),c="",s=o,f===xo){for(i++;i<u&&(f=r.charAt(i),!!sn(f));)c+=f,i++;if(f=r.charAt(i),f===sn.delimiter)o+=xo+c+f,i++;else{if(a)return;i-=c.length+1,c=""}}if(!c){for(;i<u&&(f=r.charAt(i),!!pd(f));)c+=f,i++;o+=c}if(c){for(D=c,c="";i<u&&(f=r.charAt(i),!(f!==rt&&f!==tt&&f!==Ze));)c+=f,i++;if(f=r.charAt(i),p=null,f===Ao?p=Ao:f===yo?p=yo:f===ld&&(p=fd),!p)c="",i=o.length;else if(c){for(o+=c+f,i=o.length,c="";i<u&&(f=r.charAt(i),f!==p);){if(f===Ze){if(i++,f=r.charAt(i),f===Ze||f===p)return;c+=Ze}c+=f,i++}if(f=r.charAt(i),f!==p)return;l=o,o+=c+f,i++,h=c,c=""}else return;for(;i<u&&(f=r.charAt(i),!(f!==rt&&f!==tt));)o+=f,i++;if(f=r.charAt(i),!f||f===Ze)return t?!0:(s=e(s).test().end,D=n.decode.raw(n.unescape(D),s,{nonTerminated:!1}),h&&(l=e(l).test().end,h=n.decode.raw(n.unescape(h),l)),e(o)({type:"definition",identifier:sd(d),label:d,title:h||null,url:D}))}}}}function sn(e){return e!==ko&&e!==cn&&e!==vr}sn.delimiter=ko;function pd(e){return e!==cn&&e!==vr&&!od(e)}});var So=C((uC,_o)=>{"use strict";var hd=oe();_o.exports=yd;var dd="	",nt=`
`,md=" ",Fd="-",gd=":",Ed="\\",ln="|",vd=1,Cd=2,qo="left",bd="center",Ad="right";function yd(e,r,t){var n=this,a,i,u,o,s,l,c,f,p,d,D,h,m,F,A,v,B,b,g,y,w,E;if(n.options.gfm){for(a=0,v=0,l=r.length+1,c=[];a<l;){if(y=r.indexOf(nt,a),w=r.indexOf(ln,a+1),y===-1&&(y=r.length),w===-1||w>y){if(v<Cd)return;break}c.push(r.slice(a,y)),v++,a=y+1}for(o=c.join(nt),i=c.splice(1,1)[0]||[],a=0,l=i.length,v--,u=!1,D=[];a<l;){if(p=i.charAt(a),p===ln){if(d=null,u===!1){if(E===!1)return}else D.push(u),u=!1;E=!1}else if(p===Fd)d=!0,u=u||null;else if(p===gd)u===qo?u=bd:d&&u===null?u=Ad:u=qo;else if(!hd(p))return;a++}if(u!==!1&&D.push(u),!(D.length<vd)){if(t)return!0;for(A=-1,b=[],g=e(o).reset({type:"table",align:D,children:b});++A<v;){for(B=c[A],s={type:"tableRow",children:[]},A&&e(nt),e(B).reset(s,g),l=B.length+1,a=0,f="",h="",m=!0;a<l;){if(p=B.charAt(a),p===dd||p===md){h?f+=p:e(p),a++;continue}p===""||p===ln?m?e(p):((h||p)&&!m&&(o=h,f.length>1&&(p?(o+=f.slice(0,-1),f=f.charAt(f.length-1)):(o+=f,f="")),F=e.now(),e(o)({type:"tableCell",children:n.tokenizeInline(h,F)},s)),e(f+p),f="",h=""):(f&&(h+=f,f=""),h+=p,p===Ed&&a!==l-2&&(h+=B.charAt(a+1),a++)),m=!1,a++}A||e(nt+i)}return g}}}});var Lo=C((aC,Oo)=>{"use strict";var wd=Ne(),xd=Kt(),kd=Zr();Oo.exports=qd;var Bd="	",Cr=`
`,Td=" ",Po=4;function qd(e,r,t){for(var n=this,a=n.options,i=a.commonmark,u=n.blockTokenizers,o=n.interruptParagraph,s=r.indexOf(Cr),l=r.length,c,f,p,d,D;s<l;){if(s===-1){s=l;break}if(r.charAt(s+1)===Cr)break;if(i){for(d=0,c=s+1;c<l;){if(p=r.charAt(c),p===Bd){d=Po;break}else if(p===Td)d++;else break;c++}if(d>=Po&&p!==Cr){s=r.indexOf(Cr,s+1);continue}}if(f=r.slice(s+1),kd(o,u,n,[e,f,!0]))break;if(c=s,s=r.indexOf(Cr,s+1),s!==-1&&wd(r.slice(c,s))===""){s=c;break}}return f=r.slice(0,s),t?!0:(D=e.now(),f=xd(f),e(f)({type:"paragraph",children:n.tokenizeInline(f,D)}))}});var Ro=C((oC,Io)=>{"use strict";Io.exports=_d;function _d(e,r){return e.indexOf("\\",r)}});var zo=C((sC,Uo)=>{"use strict";var Sd=Ro();Uo.exports=Mo;Mo.locator=Sd;var Pd=`
`,No="\\";function Mo(e,r,t){var n=this,a,i;if(r.charAt(0)===No&&(a=r.charAt(1),n.escape.indexOf(a)!==-1))return t?!0:(a===Pd?i={type:"break"}:i={type:"text",value:a},e(No+a)(i))}});var fn=C((cC,Go)=>{"use strict";Go.exports=Od;function Od(e,r){return e.indexOf("<",r)}});var Wo=C((lC,jo)=>{"use strict";var Yo=oe(),Ld=dr(),Id=fn();jo.exports=dn;dn.locator=Id;dn.notInLink=!0;var $o="<",Dn=">",Vo="@",pn="/",hn="mailto:",it=hn.length;function dn(e,r,t){var n=this,a="",i=r.length,u=0,o="",s=!1,l="",c,f,p,d,D;if(r.charAt(0)===$o){for(u++,a=$o;u<i&&(c=r.charAt(u),!(Yo(c)||c===Dn||c===Vo||c===":"&&r.charAt(u+1)===pn));)o+=c,u++;if(o){if(l+=o,o="",c=r.charAt(u),l+=c,u++,c===Vo)s=!0;else{if(c!==":"||r.charAt(u+1)!==pn)return;l+=pn,u++}for(;u<i&&(c=r.charAt(u),!(Yo(c)||c===Dn));)o+=c,u++;if(c=r.charAt(u),!(!o||c!==Dn))return t?!0:(l+=o,p=l,a+=l+c,f=e.now(),f.column++,f.offset++,s&&(l.slice(0,it).toLowerCase()===hn?(p=p.slice(it),f.column+=it,f.offset+=it):l=hn+l),d=n.inlineTokenizers,n.inlineTokenizers={text:d.text},D=n.enterLink(),p=n.tokenizeInline(p,f),n.inlineTokenizers=d,D(),e(a)({type:"link",title:null,url:Ld(l,{nonTerminated:!1}),children:p}))}}}});var Ko=C((fC,Ho)=>{"use strict";Ho.exports=Rd;function Rd(e,r){var t=String(e),n=0,a;if(typeof r!="string")throw new Error("Expected character");for(a=t.indexOf(r);a!==-1;)n++,a=t.indexOf(r,a+r.length);return n}});var Qo=C((DC,Jo)=>{"use strict";Jo.exports=Nd;var Xo=["www.","http://","https://"];function Nd(e,r){var t=-1,n,a,i;if(!this.options.gfm)return t;for(a=Xo.length,n=-1;++n<a;)i=e.indexOf(Xo[n],r),i!==-1&&(t===-1||i<t)&&(t=i);return t}});var ns=C((pC,ts)=>{"use strict";var Zo=Ko(),Md=dr(),Ud=Re(),mn=He(),zd=oe(),Gd=Qo();ts.exports=gn;gn.locator=Gd;gn.notInLink=!0;var Yd=33,$d=38,Vd=41,jd=42,Wd=44,Hd=45,Fn=46,Kd=58,Xd=59,Jd=63,Qd=60,es=95,Zd=126,e0="(",rs=")";function gn(e,r,t){var n=this,a=n.options.gfm,i=n.inlineTokenizers,u=r.length,o=-1,s=!1,l,c,f,p,d,D,h,m,F,A,v,B,b,g;if(a){if(r.slice(0,4)==="www.")s=!0,p=4;else if(r.slice(0,7).toLowerCase()==="http://")p=7;else if(r.slice(0,8).toLowerCase()==="https://")p=8;else return;for(o=p-1,f=p,l=[];p<u;){if(h=r.charCodeAt(p),h===Fn){if(o===p-1)break;l.push(p),o=p,p++;continue}if(Ud(h)||mn(h)||h===Hd||h===es){p++;continue}break}if(h===Fn&&(l.pop(),p--),l[0]!==void 0&&(c=l.length<2?f:l[l.length-2]+1,r.slice(c,p).indexOf("_")===-1)){if(t)return!0;for(m=p,d=p;p<u&&(h=r.charCodeAt(p),!(zd(h)||h===Qd));)p++,h===Yd||h===jd||h===Wd||h===Fn||h===Kd||h===Jd||h===es||h===Zd||(m=p);if(p=m,r.charCodeAt(p-1)===Vd)for(D=r.slice(d,p),F=Zo(D,e0),A=Zo(D,rs);A>F;)p=d+D.lastIndexOf(rs),D=r.slice(d,p),A--;if(r.charCodeAt(p-1)===Xd&&(p--,mn(r.charCodeAt(p-1)))){for(m=p-2;mn(r.charCodeAt(m));)m--;r.charCodeAt(m)===$d&&(p=m)}return v=r.slice(0,p),b=Md(v,{nonTerminated:!1}),s&&(b="http://"+b),g=n.enterLink(),n.inlineTokenizers={text:i.text},B=n.tokenizeInline(v,e.now()),n.inlineTokenizers=i,g(),e(v)({type:"link",title:null,url:b,children:B})}}}});var os=C((hC,as)=>{"use strict";var r0=Re(),t0=He(),n0=43,i0=45,u0=46,a0=95;as.exports=us;function us(e,r){var t=this,n,a;if(!this.options.gfm||(n=e.indexOf("@",r),n===-1))return-1;if(a=n,a===r||!is(e.charCodeAt(a-1)))return us.call(t,e,n+1);for(;a>r&&is(e.charCodeAt(a-1));)a--;return a}function is(e){return r0(e)||t0(e)||e===n0||e===i0||e===u0||e===a0}});var fs=C((dC,ls)=>{"use strict";var o0=dr(),ss=Re(),cs=He(),s0=os();ls.exports=Cn;Cn.locator=s0;Cn.notInLink=!0;var c0=43,En=45,ut=46,l0=64,vn=95;function Cn(e,r,t){var n=this,a=n.options.gfm,i=n.inlineTokenizers,u=0,o=r.length,s=-1,l,c,f,p;if(a){for(l=r.charCodeAt(u);ss(l)||cs(l)||l===c0||l===En||l===ut||l===vn;)l=r.charCodeAt(++u);if(u!==0&&l===l0){for(u++;u<o;){if(l=r.charCodeAt(u),ss(l)||cs(l)||l===En||l===ut||l===vn){u++,s===-1&&l===ut&&(s=u);continue}break}if(!(s===-1||s===u||l===En||l===vn))return l===ut&&u--,c=r.slice(0,u),t?!0:(p=n.enterLink(),n.inlineTokenizers={text:i.text},f=n.tokenizeInline(c,e.now()),n.inlineTokenizers=i,p(),e(c)({type:"link",title:null,url:"mailto:"+o0(c,{nonTerminated:!1}),children:f}))}}}});var hs=C((mC,ps)=>{"use strict";var f0=He(),D0=fn(),p0=an().tag;ps.exports=Ds;Ds.locator=D0;var h0="<",d0="?",m0="!",F0="/",g0=/^<a /i,E0=/^<\/a>/i;function Ds(e,r,t){var n=this,a=r.length,i,u;if(!(r.charAt(0)!==h0||a<3)&&(i=r.charAt(1),!(!f0(i)&&i!==d0&&i!==m0&&i!==F0)&&(u=r.match(p0),!!u)))return t?!0:(u=u[0],!n.inLink&&g0.test(u)?n.inLink=!0:n.inLink&&E0.test(u)&&(n.inLink=!1),e(u)({type:"html",value:u}))}});var bn=C((FC,ds)=>{"use strict";ds.exports=v0;function v0(e,r){var t=e.indexOf("[",r),n=e.indexOf("![",r);return n===-1||t<n?t:n}});var bs=C((gC,Cs)=>{"use strict";var br=oe(),C0=bn();Cs.exports=vs;vs.locator=C0;var b0=`
`,A0="!",ms='"',Fs="'",er="(",Ar=")",An="<",yn=">",gs="[",yr="\\",y0="]",Es="`";function vs(e,r,t){var n=this,a="",i=0,u=r.charAt(0),o=n.options.pedantic,s=n.options.commonmark,l=n.options.gfm,c,f,p,d,D,h,m,F,A,v,B,b,g,y,w,E,x,k;if(u===A0&&(F=!0,a=u,u=r.charAt(++i)),u===gs&&!(!F&&n.inLink)){for(a+=u,y="",i++,B=r.length,E=e.now(),g=0,E.column+=i,E.offset+=i;i<B;){if(u=r.charAt(i),h=u,u===Es){for(f=1;r.charAt(i+1)===Es;)h+=u,i++,f++;p?f>=p&&(p=0):p=f}else if(u===yr)i++,h+=r.charAt(i);else if((!p||l)&&u===gs)g++;else if((!p||l)&&u===y0)if(g)g--;else{if(r.charAt(i+1)!==er)return;h+=er,c=!0,i++;break}y+=h,h="",i++}if(c){for(A=y,a+=y+h,i++;i<B&&(u=r.charAt(i),!!br(u));)a+=u,i++;if(u=r.charAt(i),y="",d=a,u===An){for(i++,d+=An;i<B&&(u=r.charAt(i),u!==yn);){if(s&&u===b0)return;y+=u,i++}if(r.charAt(i)!==yn)return;a+=An+y+yn,w=y,i++}else{for(u=null,h="";i<B&&(u=r.charAt(i),!(h&&(u===ms||u===Fs||s&&u===er)));){if(br(u)){if(!o)break;h+=u}else{if(u===er)g++;else if(u===Ar){if(g===0)break;g--}y+=h,h="",u===yr&&(y+=yr,u=r.charAt(++i)),y+=u}i++}a+=y,w=y,i=a.length}for(y="";i<B&&(u=r.charAt(i),!!br(u));)y+=u,i++;if(u=r.charAt(i),a+=y,y&&(u===ms||u===Fs||s&&u===er))if(i++,a+=u,y="",v=u===er?Ar:u,D=a,s){for(;i<B&&(u=r.charAt(i),u!==v);)u===yr&&(y+=yr,u=r.charAt(++i)),i++,y+=u;if(u=r.charAt(i),u!==v)return;for(b=y,a+=y+u,i++;i<B&&(u=r.charAt(i),!!br(u));)a+=u,i++}else for(h="";i<B;){if(u=r.charAt(i),u===v)m&&(y+=v+h,h=""),m=!0;else if(!m)y+=u;else if(u===Ar){a+=y+v+h,b=y;break}else br(u)?h+=u:(y+=v+h+u,h="",m=!1);i++}if(r.charAt(i)===Ar)return t?!0:(a+=Ar,w=n.decode.raw(n.unescape(w),e(d).test().end,{nonTerminated:!1}),b&&(D=e(D).test().end,b=n.decode.raw(n.unescape(b),D)),k={type:F?"image":"link",title:b||null,url:w},F?k.alt=n.decode.raw(n.unescape(A),E)||null:(x=n.enterLink(),k.children=n.tokenizeInline(A,E),x()),e(a)(k))}}}});var ws=C((EC,ys)=>{"use strict";var w0=oe(),x0=bn(),k0=on();ys.exports=As;As.locator=x0;var wn="link",B0="image",T0="shortcut",q0="collapsed",xn="full",_0="!",at="[",ot="\\",st="]";function As(e,r,t){var n=this,a=n.options.commonmark,i=r.charAt(0),u=0,o=r.length,s="",l="",c=wn,f=T0,p,d,D,h,m,F,A,v;if(i===_0&&(c=B0,l=i,i=r.charAt(++u)),i===at){for(u++,l+=i,F="",v=0;u<o;){if(i=r.charAt(u),i===at)A=!0,v++;else if(i===st){if(!v)break;v--}i===ot&&(F+=ot,i=r.charAt(++u)),F+=i,u++}if(s=F,p=F,i=r.charAt(u),i===st){if(u++,s+=i,F="",!a)for(;u<o&&(i=r.charAt(u),!!w0(i));)F+=i,u++;if(i=r.charAt(u),i===at){for(d="",F+=i,u++;u<o&&(i=r.charAt(u),!(i===at||i===st));)i===ot&&(d+=ot,i=r.charAt(++u)),d+=i,u++;i=r.charAt(u),i===st?(f=d?xn:q0,F+=d+i,u++):d="",s+=F,F=""}else{if(!p)return;d=p}if(!(f!==xn&&A))return s=l+s,c===wn&&n.inLink?null:t?!0:(D=e.now(),D.column+=l.length,D.offset+=l.length,d=f===xn?d:p,h={type:c+"Reference",identifier:k0(d),label:d,referenceType:f},c===wn?(m=n.enterLink(),h.children=n.tokenizeInline(p,D),m()):h.alt=n.decode.raw(n.unescape(p),D)||null,e(s)(h))}}}});var ks=C((vC,xs)=>{"use strict";xs.exports=S0;function S0(e,r){var t=e.indexOf("**",r),n=e.indexOf("__",r);return n===-1?t:t===-1||n<t?n:t}});var _s=C((CC,qs)=>{"use strict";var P0=Ne(),Bs=oe(),O0=ks();qs.exports=Ts;Ts.locator=O0;var L0="\\",I0="*",R0="_";function Ts(e,r,t){var n=this,a=0,i=r.charAt(a),u,o,s,l,c,f,p;if(!(i!==I0&&i!==R0||r.charAt(++a)!==i)&&(o=n.options.pedantic,s=i,c=s+s,f=r.length,a++,l="",i="",!(o&&Bs(r.charAt(a)))))for(;a<f;){if(p=i,i=r.charAt(a),i===s&&r.charAt(a+1)===s&&(!o||!Bs(p))&&(i=r.charAt(a+2),i!==s))return P0(l)?t?!0:(u=e.now(),u.column+=2,u.offset+=2,e(c+l+c)({type:"strong",children:n.tokenizeInline(l,u)})):void 0;!o&&i===L0&&(l+=i,i=r.charAt(++a)),l+=i,a++}}});var Ps=C((bC,Ss)=>{"use strict";Ss.exports=U0;var N0=String.fromCharCode,M0=/\w/;function U0(e){return M0.test(typeof e=="number"?N0(e):e.charAt(0))}});var Ls=C((AC,Os)=>{"use strict";Os.exports=z0;function z0(e,r){var t=e.indexOf("*",r),n=e.indexOf("_",r);return n===-1?t:t===-1||n<t?n:t}});var Us=C((yC,Ms)=>{"use strict";var G0=Ne(),Y0=Ps(),Is=oe(),$0=Ls();Ms.exports=Ns;Ns.locator=$0;var V0="*",Rs="_",j0="\\";function Ns(e,r,t){var n=this,a=0,i=r.charAt(a),u,o,s,l,c,f,p;if(!(i!==V0&&i!==Rs)&&(o=n.options.pedantic,c=i,s=i,f=r.length,a++,l="",i="",!(o&&Is(r.charAt(a)))))for(;a<f;){if(p=i,i=r.charAt(a),i===s&&(!o||!Is(p))){if(i=r.charAt(++a),i!==s){if(!G0(l)||p===s)return;if(!o&&s===Rs&&Y0(i)){l+=s;continue}return t?!0:(u=e.now(),u.column++,u.offset++,e(c+l+s)({type:"emphasis",children:n.tokenizeInline(l,u)}))}l+=s}!o&&i===j0&&(l+=i,i=r.charAt(++a)),l+=i,a++}}});var Gs=C((wC,zs)=>{"use strict";zs.exports=W0;function W0(e,r){return e.indexOf("~~",r)}});var Ws=C((xC,js)=>{"use strict";var Ys=oe(),H0=Gs();js.exports=Vs;Vs.locator=H0;var ct="~",$s="~~";function Vs(e,r,t){var n=this,a="",i="",u="",o="",s,l,c;if(!(!n.options.gfm||r.charAt(0)!==ct||r.charAt(1)!==ct||Ys(r.charAt(2))))for(s=1,l=r.length,c=e.now(),c.column+=2,c.offset+=2;++s<l;){if(a=r.charAt(s),a===ct&&i===ct&&(!u||!Ys(u)))return t?!0:e($s+o+$s)({type:"delete",children:n.tokenizeInline(o,c)});o+=i,u=i,i=a}}});var Ks=C((kC,Hs)=>{"use strict";Hs.exports=K0;function K0(e,r){return e.indexOf("`",r)}});var Qs=C((BC,Js)=>{"use strict";var X0=Ks();Js.exports=Xs;Xs.locator=X0;var kn=10,Bn=32,Tn=96;function Xs(e,r,t){for(var n=r.length,a=0,i,u,o,s,l,c;a<n&&r.charCodeAt(a)===Tn;)a++;if(!(a===0||a===n)){for(i=a,l=r.charCodeAt(a);a<n;){if(s=l,l=r.charCodeAt(a+1),s===Tn){if(u===void 0&&(u=a),o=a+1,l!==Tn&&o-u===i){c=!0;break}}else u!==void 0&&(u=void 0,o=void 0);a++}if(c){if(t)return!0;if(a=i,n=u,s=r.charCodeAt(a),l=r.charCodeAt(n-1),c=!1,n-a>2&&(s===Bn||s===kn)&&(l===Bn||l===kn)){for(a++,n--;a<n;){if(s=r.charCodeAt(a),s!==Bn&&s!==kn){c=!0;break}a++}c===!0&&(i++,u--)}return e(r.slice(0,o))({type:"inlineCode",value:r.slice(i,u)})}}}});var ec=C((TC,Zs)=>{"use strict";Zs.exports=J0;function J0(e,r){for(var t=e.indexOf(`
`,r);t>r&&e.charAt(t-1)===" ";)t--;return t}});var nc=C((qC,tc)=>{"use strict";var Q0=ec();tc.exports=rc;rc.locator=Q0;var Z0=" ",em=`
`,rm=2;function rc(e,r,t){for(var n=r.length,a=-1,i="",u;++a<n;){if(u=r.charAt(a),u===em)return a<rm?void 0:t?!0:(i+=u,e(i)({type:"break"}));if(u!==Z0)return;i+=u}}});var uc=C((_C,ic)=>{"use strict";ic.exports=tm;function tm(e,r,t){var n=this,a,i,u,o,s,l,c,f,p,d;if(t)return!0;for(a=n.inlineMethods,o=a.length,i=n.inlineTokenizers,u=-1,p=r.length;++u<o;)f=a[u],!(f==="text"||!i[f])&&(c=i[f].locator,c||e.file.fail("Missing locator: `"+f+"`"),l=c.call(n,r,1),l!==-1&&l<p&&(p=l));s=r.slice(0,p),d=e.now(),n.decode(s,d,D);function D(h,m,F){e(F||h)({type:"text",value:h})}}});var cc=C((SC,sc)=>{"use strict";var nm=Ie(),lt=wu(),im=ku(),um=Tu(),am=na(),qn=aa();sc.exports=ac;function ac(e,r){this.file=r,this.offset={},this.options=nm(this.options),this.setOptions({}),this.inList=!1,this.inBlock=!1,this.inLink=!1,this.atStart=!0,this.toOffset=im(r).toOffset,this.unescape=um(this,"escape"),this.decode=am(this)}var M=ac.prototype;M.setOptions=ha();M.parse=qa();M.options=jt();M.exitStart=lt("atStart",!0);M.enterList=lt("inList",!1);M.enterLink=lt("inLink",!1);M.enterBlock=lt("inBlock",!1);M.interruptParagraph=[["thematicBreak"],["list"],["atxHeading"],["fencedCode"],["blockquote"],["html"],["setextHeading",{commonmark:!1}],["definition",{commonmark:!1}]];M.interruptList=[["atxHeading",{pedantic:!1}],["fencedCode",{pedantic:!1}],["thematicBreak",{pedantic:!1}],["definition",{commonmark:!1}]];M.interruptBlockquote=[["indentedCode",{commonmark:!0}],["fencedCode",{commonmark:!0}],["atxHeading",{commonmark:!0}],["setextHeading",{commonmark:!0}],["thematicBreak",{commonmark:!0}],["html",{commonmark:!0}],["list",{commonmark:!0}],["definition",{commonmark:!1}]];M.blockTokenizers={blankLine:Sa(),indentedCode:Ra(),fencedCode:Ua(),blockquote:ja(),atxHeading:Ka(),thematicBreak:Qa(),list:co(),setextHeading:po(),html:vo(),definition:To(),table:So(),paragraph:Lo()};M.inlineTokenizers={escape:zo(),autoLink:Wo(),url:ns(),email:fs(),html:hs(),link:bs(),reference:ws(),strong:_s(),emphasis:Us(),deletion:Ws(),code:Qs(),break:nc(),text:uc()};M.blockMethods=oc(M.blockTokenizers);M.inlineMethods=oc(M.inlineTokenizers);M.tokenizeBlock=qn("block");M.tokenizeInline=qn("inline");M.tokenizeFactory=qn;function oc(e){var r=[],t;for(t in e)r.push(t);return r}});var pc=C((PC,Dc)=>{"use strict";var om=Au(),sm=Ie(),lc=cc();Dc.exports=fc;fc.Parser=lc;function fc(e){var r=this.data("settings"),t=om(lc);t.prototype.options=sm(t.prototype.options,r,e),this.Parser=t}});var dc=C((OC,hc)=>{"use strict";hc.exports=cm;function cm(e){if(e)throw e}});var _n=C((LC,mc)=>{mc.exports=function(r){return r!=null&&r.constructor!=null&&typeof r.constructor.isBuffer=="function"&&r.constructor.isBuffer(r)}});var wc=C((IC,yc)=>{"use strict";var ft=Object.prototype.hasOwnProperty,Ac=Object.prototype.toString,Fc=Object.defineProperty,gc=Object.getOwnPropertyDescriptor,Ec=function(r){return typeof Array.isArray=="function"?Array.isArray(r):Ac.call(r)==="[object Array]"},vc=function(r){if(!r||Ac.call(r)!=="[object Object]")return!1;var t=ft.call(r,"constructor"),n=r.constructor&&r.constructor.prototype&&ft.call(r.constructor.prototype,"isPrototypeOf");if(r.constructor&&!t&&!n)return!1;var a;for(a in r);return typeof a>"u"||ft.call(r,a)},Cc=function(r,t){Fc&&t.name==="__proto__"?Fc(r,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):r[t.name]=t.newValue},bc=function(r,t){if(t==="__proto__")if(ft.call(r,t)){if(gc)return gc(r,t).value}else return;return r[t]};yc.exports=function e(){var r,t,n,a,i,u,o=arguments[0],s=1,l=arguments.length,c=!1;for(typeof o=="boolean"&&(c=o,o=arguments[1]||{},s=2),(o==null||typeof o!="object"&&typeof o!="function")&&(o={});s<l;++s)if(r=arguments[s],r!=null)for(t in r)n=bc(o,t),a=bc(r,t),o!==a&&(c&&a&&(vc(a)||(i=Ec(a)))?(i?(i=!1,u=n&&Ec(n)?n:[]):u=n&&vc(n)?n:{},Cc(o,{name:t,newValue:e(c,u,a)})):typeof a<"u"&&Cc(o,{name:t,newValue:a}));return o}});var kc=C((RC,xc)=>{"use strict";xc.exports=e=>{if(Object.prototype.toString.call(e)!=="[object Object]")return!1;let r=Object.getPrototypeOf(e);return r===null||r===Object.prototype}});var Tc=C((NC,Bc)=>{"use strict";var lm=[].slice;Bc.exports=fm;function fm(e,r){var t;return n;function n(){var u=lm.call(arguments,0),o=e.length>u.length,s;o&&u.push(a);try{s=e.apply(null,u)}catch(l){if(o&&t)throw l;return a(l)}o||(s&&typeof s.then=="function"?s.then(i,a):s instanceof Error?a(s):i(s))}function a(){t||(t=!0,r.apply(null,arguments))}function i(u){a(null,u)}}});var Oc=C((MC,Pc)=>{"use strict";var _c=Tc();Pc.exports=Sc;Sc.wrap=_c;var qc=[].slice;function Sc(){var e=[],r={};return r.run=t,r.use=n,r;function t(){var a=-1,i=qc.call(arguments,0,-1),u=arguments[arguments.length-1];if(typeof u!="function")throw new Error("Expected function as last argument, not "+u);o.apply(null,[null].concat(i));function o(s){var l=e[++a],c=qc.call(arguments,0),f=c.slice(1),p=i.length,d=-1;if(s){u(s);return}for(;++d<p;)(f[d]===null||f[d]===void 0)&&(f[d]=i[d]);i=f,l?_c(l,o).apply(null,i):u.apply(null,[null].concat(i))}}function n(a){if(typeof a!="function")throw new Error("Expected `fn` to be a function, not "+a);return e.push(a),r}}});var Nc=C((UC,Rc)=>{"use strict";var rr={}.hasOwnProperty;Rc.exports=Dm;function Dm(e){return!e||typeof e!="object"?"":rr.call(e,"position")||rr.call(e,"type")?Lc(e.position):rr.call(e,"start")||rr.call(e,"end")?Lc(e):rr.call(e,"line")||rr.call(e,"column")?Sn(e):""}function Sn(e){return(!e||typeof e!="object")&&(e={}),Ic(e.line)+":"+Ic(e.column)}function Lc(e){return(!e||typeof e!="object")&&(e={}),Sn(e.start)+"-"+Sn(e.end)}function Ic(e){return e&&typeof e=="number"?e:1}});var zc=C((zC,Uc)=>{"use strict";var pm=Nc();Uc.exports=Pn;function Mc(){}Mc.prototype=Error.prototype;Pn.prototype=new Mc;var ke=Pn.prototype;ke.file="";ke.name="";ke.reason="";ke.message="";ke.stack="";ke.fatal=null;ke.column=null;ke.line=null;function Pn(e,r,t){var n,a,i;typeof r=="string"&&(t=r,r=null),n=hm(t),a=pm(r)||"1:1",i={start:{line:null,column:null},end:{line:null,column:null}},r&&r.position&&(r=r.position),r&&(r.start?(i=r,r=r.start):i.start=r),e.stack&&(this.stack=e.stack,e=e.message),this.message=e,this.name=a,this.reason=e,this.line=r?r.line:null,this.column=r?r.column:null,this.location=i,this.source=n[0],this.ruleId=n[1]}function hm(e){var r=[null,null],t;return typeof e=="string"&&(t=e.indexOf(":"),t===-1?r[1]=e:(r[0]=e.slice(0,t),r[1]=e.slice(t+1))),r}});var Gc=C(tr=>{"use strict";tr.basename=dm;tr.dirname=mm;tr.extname=Fm;tr.join=gm;tr.sep="/";function dm(e,r){var t=0,n=-1,a,i,u,o;if(r!==void 0&&typeof r!="string")throw new TypeError('"ext" argument must be a string');if(wr(e),a=e.length,r===void 0||!r.length||r.length>e.length){for(;a--;)if(e.charCodeAt(a)===47){if(u){t=a+1;break}}else n<0&&(u=!0,n=a+1);return n<0?"":e.slice(t,n)}if(r===e)return"";for(i=-1,o=r.length-1;a--;)if(e.charCodeAt(a)===47){if(u){t=a+1;break}}else i<0&&(u=!0,i=a+1),o>-1&&(e.charCodeAt(a)===r.charCodeAt(o--)?o<0&&(n=a):(o=-1,n=i));return t===n?n=i:n<0&&(n=e.length),e.slice(t,n)}function mm(e){var r,t,n;if(wr(e),!e.length)return".";for(r=-1,n=e.length;--n;)if(e.charCodeAt(n)===47){if(t){r=n;break}}else t||(t=!0);return r<0?e.charCodeAt(0)===47?"/":".":r===1&&e.charCodeAt(0)===47?"//":e.slice(0,r)}function Fm(e){var r=-1,t=0,n=-1,a=0,i,u,o;for(wr(e),o=e.length;o--;){if(u=e.charCodeAt(o),u===47){if(i){t=o+1;break}continue}n<0&&(i=!0,n=o+1),u===46?r<0?r=o:a!==1&&(a=1):r>-1&&(a=-1)}return r<0||n<0||a===0||a===1&&r===n-1&&r===t+1?"":e.slice(r,n)}function gm(){for(var e=-1,r;++e<arguments.length;)wr(arguments[e]),arguments[e]&&(r=r===void 0?arguments[e]:r+"/"+arguments[e]);return r===void 0?".":Em(r)}function Em(e){var r,t;return wr(e),r=e.charCodeAt(0)===47,t=vm(e,!r),!t.length&&!r&&(t="."),t.length&&e.charCodeAt(e.length-1)===47&&(t+="/"),r?"/"+t:t}function vm(e,r){for(var t="",n=0,a=-1,i=0,u=-1,o,s;++u<=e.length;){if(u<e.length)o=e.charCodeAt(u);else{if(o===47)break;o=47}if(o===47){if(!(a===u-1||i===1))if(a!==u-1&&i===2){if(t.length<2||n!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){if(s=t.lastIndexOf("/"),s!==t.length-1){s<0?(t="",n=0):(t=t.slice(0,s),n=t.length-1-t.lastIndexOf("/")),a=u,i=0;continue}}else if(t.length){t="",n=0,a=u,i=0;continue}}r&&(t=t.length?t+"/..":"..",n=2)}else t.length?t+="/"+e.slice(a+1,u):t=e.slice(a+1,u),n=u-a-1;a=u,i=0}else o===46&&i>-1?i++:i=-1}return t}function wr(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}});var $c=C(Yc=>{"use strict";Yc.cwd=Cm;function Cm(){return"/"}});var Wc=C(($C,jc)=>{"use strict";var se=Gc(),bm=$c(),Am=_n();jc.exports=ge;var ym={}.hasOwnProperty,On=["history","path","basename","stem","extname","dirname"];ge.prototype.toString=Lm;Object.defineProperty(ge.prototype,"path",{get:wm,set:xm});Object.defineProperty(ge.prototype,"dirname",{get:km,set:Bm});Object.defineProperty(ge.prototype,"basename",{get:Tm,set:qm});Object.defineProperty(ge.prototype,"extname",{get:_m,set:Sm});Object.defineProperty(ge.prototype,"stem",{get:Pm,set:Om});function ge(e){var r,t;if(!e)e={};else if(typeof e=="string"||Am(e))e={contents:e};else if("message"in e&&"messages"in e)return e;if(!(this instanceof ge))return new ge(e);for(this.data={},this.messages=[],this.history=[],this.cwd=bm.cwd(),t=-1;++t<On.length;)r=On[t],ym.call(e,r)&&(this[r]=e[r]);for(r in e)On.indexOf(r)<0&&(this[r]=e[r])}function wm(){return this.history[this.history.length-1]}function xm(e){In(e,"path"),this.path!==e&&this.history.push(e)}function km(){return typeof this.path=="string"?se.dirname(this.path):void 0}function Bm(e){Vc(this.path,"dirname"),this.path=se.join(e||"",this.basename)}function Tm(){return typeof this.path=="string"?se.basename(this.path):void 0}function qm(e){In(e,"basename"),Ln(e,"basename"),this.path=se.join(this.dirname||"",e)}function _m(){return typeof this.path=="string"?se.extname(this.path):void 0}function Sm(e){if(Ln(e,"extname"),Vc(this.path,"extname"),e){if(e.charCodeAt(0)!==46)throw new Error("`extname` must start with `.`");if(e.indexOf(".",1)>-1)throw new Error("`extname` cannot contain multiple dots")}this.path=se.join(this.dirname,this.stem+(e||""))}function Pm(){return typeof this.path=="string"?se.basename(this.path,this.extname):void 0}function Om(e){In(e,"stem"),Ln(e,"stem"),this.path=se.join(this.dirname||"",e+(this.extname||""))}function Lm(e){return(this.contents||"").toString(e)}function Ln(e,r){if(e&&e.indexOf(se.sep)>-1)throw new Error("`"+r+"` cannot be a path: did not expect `"+se.sep+"`")}function In(e,r){if(!e)throw new Error("`"+r+"` cannot be empty")}function Vc(e,r){if(!e)throw new Error("Setting `"+r+"` requires `path` to be set too")}});var Kc=C((VC,Hc)=>{"use strict";var Im=zc(),Dt=Wc();Hc.exports=Dt;Dt.prototype.message=Rm;Dt.prototype.info=Mm;Dt.prototype.fail=Nm;function Rm(e,r,t){var n=new Im(e,r,t);return this.path&&(n.name=this.path+":"+n.name,n.file=this.path),n.fatal=!1,this.messages.push(n),n}function Nm(){var e=this.message.apply(this,arguments);throw e.fatal=!0,e}function Mm(){var e=this.message.apply(this,arguments);return e.fatal=null,e}});var Jc=C((jC,Xc)=>{"use strict";Xc.exports=Kc()});var al=C((WC,ul)=>{"use strict";var Qc=dc(),Um=_n(),pt=wc(),Zc=kc(),nl=Oc(),xr=Jc();ul.exports=il().freeze();var zm=[].slice,Gm={}.hasOwnProperty,Ym=nl().use($m).use(Vm).use(jm);function $m(e,r){r.tree=e.parse(r.file)}function Vm(e,r,t){e.run(r.tree,r.file,n);function n(a,i,u){a?t(a):(r.tree=i,r.file=u,t())}}function jm(e,r){var t=e.stringify(r.tree,r.file);t==null||(typeof t=="string"||Um(t)?("value"in r.file&&(r.file.value=t),r.file.contents=t):r.file.result=t)}function il(){var e=[],r=nl(),t={},n=-1,a;return i.data=o,i.freeze=u,i.attachers=e,i.use=s,i.parse=c,i.stringify=d,i.run=f,i.runSync=p,i.process=D,i.processSync=h,i;function i(){for(var m=il(),F=-1;++F<e.length;)m.use.apply(null,e[F]);return m.data(pt(!0,{},t)),m}function u(){var m,F;if(a)return i;for(;++n<e.length;)m=e[n],m[1]!==!1&&(m[1]===!0&&(m[1]=void 0),F=m[0].apply(i,m.slice(1)),typeof F=="function"&&r.use(F));return a=!0,n=1/0,i}function o(m,F){return typeof m=="string"?arguments.length===2?(Mn("data",a),t[m]=F,i):Gm.call(t,m)&&t[m]||null:m?(Mn("data",a),t=m,i):t}function s(m){var F;if(Mn("use",a),m!=null)if(typeof m=="function")b.apply(null,arguments);else if(typeof m=="object")"length"in m?B(m):A(m);else throw new Error("Expected usable value, not `"+m+"`");return F&&(t.settings=pt(t.settings||{},F)),i;function A(g){B(g.plugins),g.settings&&(F=pt(F||{},g.settings))}function v(g){if(typeof g=="function")b(g);else if(typeof g=="object")"length"in g?b.apply(null,g):A(g);else throw new Error("Expected usable value, not `"+g+"`")}function B(g){var y=-1;if(g!=null)if(typeof g=="object"&&"length"in g)for(;++y<g.length;)v(g[y]);else throw new Error("Expected a list of plugins, not `"+g+"`")}function b(g,y){var w=l(g);w?(Zc(w[1])&&Zc(y)&&(y=pt(!0,w[1],y)),w[1]=y):e.push(zm.call(arguments))}}function l(m){for(var F=-1;++F<e.length;)if(e[F][0]===m)return e[F]}function c(m){var F=xr(m),A;return u(),A=i.Parser,Rn("parse",A),el(A,"parse")?new A(String(F),F).parse():A(String(F),F)}function f(m,F,A){if(rl(m),u(),!A&&typeof F=="function"&&(A=F,F=null),!A)return new Promise(v);v(null,A);function v(B,b){r.run(m,xr(F),g);function g(y,w,E){w=w||m,y?b(y):B?B(w):A(null,w,E)}}}function p(m,F){var A,v;return f(m,F,B),tl("runSync","run",v),A;function B(b,g){v=!0,A=g,Qc(b)}}function d(m,F){var A=xr(F),v;return u(),v=i.Compiler,Nn("stringify",v),rl(m),el(v,"compile")?new v(m,A).compile():v(m,A)}function D(m,F){if(u(),Rn("process",i.Parser),Nn("process",i.Compiler),!F)return new Promise(A);A(null,F);function A(v,B){var b=xr(m);Ym.run(i,{file:b},g);function g(y){y?B(y):v?v(b):F(null,b)}}}function h(m){var F,A;return u(),Rn("processSync",i.Parser),Nn("processSync",i.Compiler),F=xr(m),D(F,v),tl("processSync","process",A),F;function v(B){A=!0,Qc(B)}}}function el(e,r){return typeof e=="function"&&e.prototype&&(Wm(e.prototype)||r in e.prototype)}function Wm(e){var r;for(r in e)return!0;return!1}function Rn(e,r){if(typeof r!="function")throw new Error("Cannot `"+e+"` without `Parser`")}function Nn(e,r){if(typeof r!="function")throw new Error("Cannot `"+e+"` without `Compiler`")}function Mn(e,r){if(r)throw new Error("Cannot invoke `"+e+"` on a frozen processor.\nCreate a new processor first, by invoking it: use `processor()` instead of `processor`.")}function rl(e){if(!e||typeof e.type!="string")throw new Error("Expected node, got `"+e+"`")}function tl(e,r,t){if(!t)throw new Error("`"+e+"` finished async. Use `"+r+"` instead")}});var uF={};Yn(uF,{languages:()=>ru,options:()=>tu,parsers:()=>zn,printers:()=>iF});var ql=(e,r,t)=>{if(!(e&&r==null))return Array.isArray(r)||typeof r=="string"?r[t<0?r.length+t:t]:r.at(t)},z=ql;var _l=(e,r,t,n)=>{if(!(e&&r==null))return r.replaceAll?r.replaceAll(t,n):t.global?r.replace(t,n):r.split(t).join(n)},R=_l;var Zi=Me(Br(),1);function le(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var Y="string",H="array",Ce="cursor",re="indent",te="align",fe="trim",X="group",J="fill",K="if-break",De="indent-if-break",pe="line-suffix",he="line-suffix-boundary",$="line",de="label",ne="break-parent",Tr=new Set([Ce,re,te,fe,X,J,K,De,pe,he,$,de,ne]);function Pl(e){if(typeof e=="string")return Y;if(Array.isArray(e))return H;if(!e)return;let{type:r}=e;if(Tr.has(r))return r}var G=Pl;var Ol=e=>new Intl.ListFormat("en-US",{type:"disjunction"}).format(e);function Ll(e){let r=e===null?"null":typeof e;if(r!=="string"&&r!=="object")return`Unexpected doc '${r}', 
Expected it to be 'string' or 'object'.`;if(G(e))throw new Error("doc is valid.");let t=Object.prototype.toString.call(e);if(t!=="[object Object]")return`Unexpected doc '${t}'.`;let n=Ol([...Tr].map(a=>`'${a}'`));return`Unexpected doc.type '${e.type}'.
Expected it to be ${n}.`}var dt=class extends Error{name="InvalidDocError";constructor(r){super(Ll(r)),this.doc=r}},Te=dt;var Kn={};function Il(e,r,t,n){let a=[e];for(;a.length>0;){let i=a.pop();if(i===Kn){t(a.pop());continue}t&&a.push(i,Kn);let u=G(i);if(!u)throw new Te(i);if((r==null?void 0:r(i))!==!1)switch(u){case H:case J:{let o=u===H?i:i.parts;for(let s=o.length,l=s-1;l>=0;--l)a.push(o[l]);break}case K:a.push(i.flatContents,i.breakContents);break;case X:if(n&&i.expandedStates)for(let o=i.expandedStates.length,s=o-1;s>=0;--s)a.push(i.expandedStates[s]);else a.push(i.contents);break;case te:case re:case De:case de:case pe:a.push(i.contents);break;case Y:case Ce:case fe:case he:case $:case ne:break;default:throw new Te(i)}}}var mt=Il;function Rl(e,r){if(typeof e=="string")return r(e);let t=new Map;return n(e);function n(i){if(t.has(i))return t.get(i);let u=a(i);return t.set(i,u),u}function a(i){switch(G(i)){case H:return r(i.map(n));case J:return r({...i,parts:i.parts.map(n)});case K:return r({...i,breakContents:n(i.breakContents),flatContents:n(i.flatContents)});case X:{let{expandedStates:u,contents:o}=i;return u?(u=u.map(n),o=u[0]):o=n(o),r({...i,contents:o,expandedStates:u})}case te:case re:case De:case de:case pe:return r({...i,contents:n(i.contents)});case Y:case Ce:case fe:case he:case $:case ne:return r(i);default:throw new Te(i)}}}function Xn(e){if(e.length>0){let r=z(!1,e,-1);!r.expandedStates&&!r.break&&(r.break="propagated")}return null}function Jn(e){let r=new Set,t=[];function n(i){if(i.type===ne&&Xn(t),i.type===X){if(t.push(i),r.has(i))return!1;r.add(i)}}function a(i){i.type===X&&t.pop().break&&Xn(t)}mt(e,n,a,!0)}function be(e,r=nr){return Rl(e,t=>typeof t=="string"?qr(r,t.split(`
`)):t)}var Ft=()=>{},qe=Ft,gt=Ft,Qn=Ft;function ir(e){return qe(e),{type:re,contents:e}}function Ae(e,r){return qe(r),{type:te,contents:r,n:e}}function Ue(e,r={}){return qe(e),gt(r.expandedStates,!0),{type:X,id:r.id,contents:e,break:!!r.shouldBreak,expandedStates:r.expandedStates}}function _e(e){return Ae({type:"root"},e)}function ze(e){return Qn(e),{type:J,parts:e}}function Zn(e,r="",t={}){return qe(e),r!==""&&qe(r),{type:K,breakContents:e,flatContents:r,groupId:t.groupId}}var ur={type:ne};var ar={type:$,hard:!0},Nl={type:$,hard:!0,literal:!0},_r={type:$},Sr={type:$,soft:!0},L=[ar,ur],nr=[Nl,ur];function qr(e,r){qe(e),gt(r);let t=[];for(let n=0;n<r.length;n++)n!==0&&t.push(e),t.push(r[n]);return t}function Ml(e,r){let t=e.match(new RegExp(`(${le(r)})+`,"gu"));return t===null?0:t.reduce((n,a)=>Math.max(n,a.length/r.length),0)}var Pr=Ml;function Ul(e,r){let t=e.match(new RegExp(`(${le(r)})+`,"gu"));if(t===null)return 0;let n=new Map,a=0;for(let i of t){let u=i.length/r.length;n.set(u,!0),u>a&&(a=u)}for(let i=1;i<a;i++)if(!n.get(i))return i;return a+1}var ei=Ul;var Or="'",ri='"';function zl(e,r){let t=r===!0||r===Or?Or:ri,n=t===Or?ri:Or,a=0,i=0;for(let u of e)u===t?a++:u===n&&i++;return a>i?n:t}var ti=zl;var Et=class extends Error{name="UnexpectedNodeError";constructor(r,t,n="type"){super(`Unexpected ${t} node ${n}: ${JSON.stringify(r[n])}.`),this.node=r}},ni=Et;var li=Me(Br(),1);function Gl(e){return(e==null?void 0:e.type)==="front-matter"}var ii=Gl;var ui=["noformat","noprettier"],Lr=["format","prettier"],ai="format";var or=3;function Yl(e){let r=e.slice(0,or);if(r!=="---"&&r!=="+++")return;let t=e.indexOf(`
`,or);if(t===-1)return;let n=e.slice(or,t).trim(),a=e.indexOf(`
${r}`,t),i=n;if(i||(i=r==="+++"?"toml":"yaml"),a===-1&&r==="---"&&i==="yaml"&&(a=e.indexOf(`
...`,t)),a===-1)return;let u=a+1+or,o=e.charAt(u+1);if(!/\s?/u.test(o))return;let s=e.slice(0,u);return{type:"front-matter",language:i,explicitLanguage:n,value:e.slice(t+1,a),startDelimiter:r,endDelimiter:s.slice(-or),raw:s}}function $l(e){let r=Yl(e);if(!r)return{content:e};let{raw:t}=r;return{frontMatter:r,content:R(!1,t,/[^\n]/gu," ")+e.slice(t.length)}}var Ge=$l;function Ir(e,r){let t=`@(${r.join("|")})`,n=new RegExp([`<!--\\s*${t}\\s*-->`,`\\{\\s*\\/\\*\\s*${t}\\s*\\*\\/\\s*\\}`,`<!--.*\r?
[\\s\\S]*(^|
)[^\\S
]*${t}[^\\S
]*($|
)[\\s\\S]*
.*-->`].join("|"),"mu"),a=e.match(n);return(a==null?void 0:a.index)===0}var oi=e=>Ir(Ge(e).content.trimStart(),Lr),si=e=>Ir(Ge(e).content.trimStart(),ui),ci=e=>{let r=Ge(e),t=`<!-- @${ai} -->`;return r.frontMatter?`${r.frontMatter.raw}

${t}

${r.content}`:`${t}

${r.content}`};var Vl=new Set(["position","raw"]);function fi(e,r,t){if((e.type==="front-matter"||e.type==="code"||e.type==="yaml"||e.type==="import"||e.type==="export"||e.type==="jsx")&&delete r.value,e.type==="list"&&delete r.isAligned,(e.type==="list"||e.type==="listItem")&&delete r.spread,e.type==="text")return null;if(e.type==="inlineCode"&&(r.value=R(!1,e.value,`
`," ")),e.type==="wikiLink"&&(r.value=R(!1,e.value.trim(),/[\t\n]+/gu," ")),(e.type==="definition"||e.type==="linkReference"||e.type==="imageReference")&&(r.label=(0,li.default)(e.label)),(e.type==="link"||e.type==="image")&&e.url&&e.url.includes("("))for(let n of"<>")r.url=R(!1,e.url,n,encodeURIComponent(n));if((e.type==="definition"||e.type==="link"||e.type==="image")&&e.title&&(r.title=R(!1,e.title,/\\(?=["')])/gu,"")),(t==null?void 0:t.type)==="root"&&t.children.length>0&&(t.children[0]===e||ii(t.children[0])&&t.children[1]===e)&&e.type==="html"&&Ir(e.value,Lr))return null}fi.ignoredProperties=Vl;var Di=fi;var pi=/(?:[\u{2c7}\u{2c9}-\u{2cb}\u{2d9}\u{2ea}-\u{2eb}\u{305}\u{323}\u{1100}-\u{11ff}\u{2e80}-\u{2e99}\u{2e9b}-\u{2ef3}\u{2f00}-\u{2fd5}\u{2ff0}-\u{303f}\u{3041}-\u{3096}\u{3099}-\u{30ff}\u{3105}-\u{312f}\u{3131}-\u{318e}\u{3190}-\u{4dbf}\u{4e00}-\u{9fff}\u{a700}-\u{a707}\u{a960}-\u{a97c}\u{ac00}-\u{d7a3}\u{d7b0}-\u{d7c6}\u{d7cb}-\u{d7fb}\u{f900}-\u{fa6d}\u{fa70}-\u{fad9}\u{fe10}-\u{fe1f}\u{fe30}-\u{fe6f}\u{ff00}-\u{ffef}\u{16fe3}\u{1aff0}-\u{1aff3}\u{1aff5}-\u{1affb}\u{1affd}-\u{1affe}\u{1b000}-\u{1b122}\u{1b132}\u{1b150}-\u{1b152}\u{1b155}\u{1b164}-\u{1b167}\u{1f200}\u{1f250}-\u{1f251}\u{20000}-\u{2a6df}\u{2a700}-\u{2b739}\u{2b740}-\u{2b81d}\u{2b820}-\u{2cea1}\u{2ceb0}-\u{2ebe0}\u{2ebf0}-\u{2ee5d}\u{2f800}-\u{2fa1d}\u{30000}-\u{3134a}\u{31350}-\u{323af}])(?:[\u{fe00}-\u{fe0f}\u{e0100}-\u{e01ef}])?/u,Se=/(?:[\u{21}-\u{2f}\u{3a}-\u{40}\u{5b}-\u{60}\u{7b}-\u{7e}]|\p{General_Category=Connector_Punctuation}|\p{General_Category=Dash_Punctuation}|\p{General_Category=Close_Punctuation}|\p{General_Category=Final_Punctuation}|\p{General_Category=Initial_Punctuation}|\p{General_Category=Other_Punctuation}|\p{General_Category=Open_Punctuation})/u;async function jl(e,r){if(e.language==="yaml"){let t=e.value.trim(),n=t?await r(t,{parser:"yaml"}):"";return _e([e.startDelimiter,e.explicitLanguage,L,n,n?L:"",e.endDelimiter])}}var hi=jl;var Wl=(e,r)=>{if(!(e&&r==null))return r.toReversed||!Array.isArray(r)?r.toReversed():[...r].reverse()},di=Wl;var mi,Fi,gi,Ei,vi,Hl=((mi=globalThis.Deno)==null?void 0:mi.build.os)==="windows"||((gi=(Fi=globalThis.navigator)==null?void 0:Fi.platform)==null?void 0:gi.startsWith("Win"))||((vi=(Ei=globalThis.process)==null?void 0:Ei.platform)==null?void 0:vi.startsWith("win"))||!1;function Ci(e){if(e=e instanceof URL?e:new URL(e),e.protocol!=="file:")throw new TypeError(`URL must be a file URL: received "${e.protocol}"`);return e}function Kl(e){return e=Ci(e),decodeURIComponent(e.pathname.replace(/%(?![0-9A-Fa-f]{2})/g,"%25"))}function Xl(e){e=Ci(e);let r=decodeURIComponent(e.pathname.replace(/\//g,"\\").replace(/%(?![0-9A-Fa-f]{2})/g,"%25")).replace(/^\\*([A-Za-z]:)(\\|$)/,"$1\\");return e.hostname!==""&&(r=`\\\\${e.hostname}${r}`),r}function bi(e){return Hl?Xl(e):Kl(e)}var Ai=bi;var Jl=e=>String(e).split(/[/\\]/u).pop();function yi(e,r){if(!r)return;let t=Jl(r).toLowerCase();return e.find(({filenames:n})=>n==null?void 0:n.some(a=>a.toLowerCase()===t))??e.find(({extensions:n})=>n==null?void 0:n.some(a=>t.endsWith(a)))}function Ql(e,r){if(r)return e.find(({name:t})=>t.toLowerCase()===r)??e.find(({aliases:t})=>t==null?void 0:t.includes(r))??e.find(({extensions:t})=>t==null?void 0:t.includes(`.${r}`))}function wi(e,r){if(r){if(String(r).startsWith("file:"))try{r=Ai(r)}catch{return}if(typeof r=="string")return e.find(({isSupported:t})=>t==null?void 0:t({filepath:r}))}}function Zl(e,r){let t=di(!1,e.plugins).flatMap(a=>a.languages??[]),n=Ql(t,r.language)??yi(t,r.physicalFile)??yi(t,r.file)??wi(t,r.physicalFile)??wi(t,r.file)??(r.physicalFile,void 0);return n==null?void 0:n.parsers[0]}var xi=Zl;var ef=new Proxy(()=>{},{get:()=>ef});function Pe(e){return e.position.start.offset}function Oe(e){return e.position.end.offset}var vt=new Set(["liquidNode","inlineCode","emphasis","esComment","strong","delete","wikiLink","link","linkReference","image","imageReference","footnote","footnoteReference","sentence","whitespace","word","break","inlineMath"]),Rr=new Set([...vt,"tableCell","paragraph","heading"]),$e="non-cjk",ie="cj-letter",Le="k-letter",sr="cjk-punctuation",rf=/\p{Script_Extensions=Hangul}/u;function Nr(e){let r=[],t=e.split(/([\t\n ]+)/u);for(let[a,i]of t.entries()){if(a%2===1){r.push({type:"whitespace",value:/\n/u.test(i)?`
`:" "});continue}if((a===0||a===t.length-1)&&i==="")continue;let u=i.split(new RegExp(`(${pi.source})`,"u"));for(let[o,s]of u.entries())if(!((o===0||o===u.length-1)&&s==="")){if(o%2===0){s!==""&&n({type:"word",value:s,kind:$e,isCJ:!1,hasLeadingPunctuation:Se.test(s[0]),hasTrailingPunctuation:Se.test(z(!1,s,-1))});continue}if(Se.test(s)){n({type:"word",value:s,kind:sr,isCJ:!0,hasLeadingPunctuation:!0,hasTrailingPunctuation:!0});continue}if(rf.test(s)){n({type:"word",value:s,kind:Le,isCJ:!1,hasLeadingPunctuation:!1,hasTrailingPunctuation:!1});continue}n({type:"word",value:s,kind:ie,isCJ:!0,hasLeadingPunctuation:!1,hasTrailingPunctuation:!1})}}return r;function n(a){let i=z(!1,r,-1);(i==null?void 0:i.type)==="word"&&!u($e,sr)&&![i.value,a.value].some(o=>/\u3000/u.test(o))&&r.push({type:"whitespace",value:""}),r.push(a);function u(o,s){return i.kind===o&&a.kind===s||i.kind===s&&a.kind===o}}}function Ye(e,r){let t=r.originalText.slice(e.position.start.offset,e.position.end.offset),{numberText:n,leadingSpaces:a}=t.match(/^\s*(?<numberText>\d+)(\.|\))(?<leadingSpaces>\s*)/u).groups;return{number:Number(n),leadingSpaces:a}}function ki(e,r){return!e.ordered||e.children.length<2||Ye(e.children[1],r).number!==1?!1:Ye(e.children[0],r).number!==0?!0:e.children.length>2&&Ye(e.children[2],r).number===1}function Mr(e,r){let{value:t}=e;return e.position.end.offset===r.length&&t.endsWith(`
`)&&r.endsWith(`
`)?t.slice(0,-1):t}function ye(e,r){return function t(n,a,i){let u={...r(n,a,i)};return u.children&&(u.children=u.children.map((o,s)=>t(o,s,[u,...i]))),u}(e,null,[])}function Ct(e){if((e==null?void 0:e.type)!=="link"||e.children.length!==1)return!1;let[r]=e.children;return Pe(e)===Pe(r)&&Oe(e)===Oe(r)}function tf(e,r){let{node:t}=e;if(t.type==="code"&&t.lang!==null){let n=xi(r,{language:t.lang});if(n)return async a=>{let i=r.__inJsTemplate?"~":"`",u=i.repeat(Math.max(3,Pr(t.value,i)+1)),o={parser:n};t.lang==="ts"||t.lang==="typescript"?o.filepath="dummy.ts":t.lang==="tsx"&&(o.filepath="dummy.tsx");let s=await a(Mr(t,r.originalText),o);return _e([u,t.lang,t.meta?" "+t.meta:"",L,be(s),L,u])}}switch(t.type){case"front-matter":return n=>hi(t,n);case"import":case"export":return n=>n(t.value,{parser:"babel"});case"jsx":return n=>n(`<$>${t.value}</$>`,{parser:"__js_expression",rootMarker:"mdx"})}return null}var Bi=tf;var cr=null;function lr(e){if(cr!==null&&typeof cr.property){let r=cr;return cr=lr.prototype=null,r}return cr=lr.prototype=e??Object.create(null),new lr}var nf=10;for(let e=0;e<=nf;e++)lr();function bt(e){return lr(e)}function uf(e,r="type"){bt(e);function t(n){let a=n[r],i=e[a];if(!Array.isArray(i))throw Object.assign(new Error(`Missing visitor keys for '${a}'.`),{node:n});return i}return t}var Ti=uf;var af={"front-matter":[],root:["children"],paragraph:["children"],sentence:["children"],word:[],whitespace:[],emphasis:["children"],strong:["children"],delete:["children"],inlineCode:[],wikiLink:[],link:["children"],image:[],blockquote:["children"],heading:["children"],code:[],html:[],list:["children"],thematicBreak:[],linkReference:["children"],imageReference:[],definition:[],footnote:["children"],footnoteReference:[],footnoteDefinition:["children"],table:["children"],tableCell:["children"],break:[],liquidNode:[],import:[],export:[],esComment:[],jsx:[],math:[],inlineMath:[],tableRow:["children"],listItem:["children"],text:[]},qi=af;var of=Ti(qi),_i=of;function Si(e){switch(e){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}var Pi=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;function Oi(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function Li(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}var Ii=e=>!(Oi(e)||Li(e));var sf=/[^\x20-\x7F]/u;function cf(e){if(!e)return 0;if(!sf.test(e))return e.length;e=e.replace(Pi(),"  ");let r=0;for(let t of e){let n=t.codePointAt(0);n<=31||n>=127&&n<=159||n>=768&&n<=879||(r+=Ii(n)?1:2)}return r}var fr=cf;var V=Symbol("MODE_BREAK"),ue=Symbol("MODE_FLAT"),Ve=Symbol("cursor"),At=Symbol("DOC_FILL_PRINTED_LENGTH");function Ri(){return{value:"",length:0,queue:[]}}function lf(e,r){return yt(e,{type:"indent"},r)}function ff(e,r,t){return r===Number.NEGATIVE_INFINITY?e.root||Ri():r<0?yt(e,{type:"dedent"},t):r?r.type==="root"?{...e,root:e}:yt(e,{type:typeof r=="string"?"stringAlign":"numberAlign",n:r},t):e}function yt(e,r,t){let n=r.type==="dedent"?e.queue.slice(0,-1):[...e.queue,r],a="",i=0,u=0,o=0;for(let D of n)switch(D.type){case"indent":c(),t.useTabs?s(1):l(t.tabWidth);break;case"stringAlign":c(),a+=D.n,i+=D.n.length;break;case"numberAlign":u+=1,o+=D.n;break;default:throw new Error(`Unexpected type '${D.type}'`)}return p(),{...e,value:a,length:i,queue:n};function s(D){a+="	".repeat(D),i+=t.tabWidth*D}function l(D){a+=" ".repeat(D),i+=D}function c(){t.useTabs?f():p()}function f(){u>0&&s(u),d()}function p(){o>0&&l(o),d()}function d(){u=0,o=0}}function wt(e){let r=0,t=0,n=e.length;e:for(;n--;){let a=e[n];if(a===Ve){t++;continue}for(let i=a.length-1;i>=0;i--){let u=a[i];if(u===" "||u==="	")r++;else{e[n]=a.slice(0,i+1);break e}}}if(r>0||t>0)for(e.length=n+1;t-- >0;)e.push(Ve);return r}function Ur(e,r,t,n,a,i){if(t===Number.POSITIVE_INFINITY)return!0;let u=r.length,o=[e],s=[];for(;t>=0;){if(o.length===0){if(u===0)return!0;o.push(r[--u]);continue}let{mode:l,doc:c}=o.pop(),f=G(c);switch(f){case Y:s.push(c),t-=fr(c);break;case H:case J:{let p=f===H?c:c.parts,d=c[At]??0;for(let D=p.length-1;D>=d;D--)o.push({mode:l,doc:p[D]});break}case re:case te:case De:case de:o.push({mode:l,doc:c.contents});break;case fe:t+=wt(s);break;case X:{if(i&&c.break)return!1;let p=c.break?V:l,d=c.expandedStates&&p===V?z(!1,c.expandedStates,-1):c.contents;o.push({mode:p,doc:d});break}case K:{let d=(c.groupId?a[c.groupId]||ue:l)===V?c.breakContents:c.flatContents;d&&o.push({mode:l,doc:d});break}case $:if(l===V||c.hard)return!0;c.soft||(s.push(" "),t--);break;case pe:n=!0;break;case he:if(n)return!1;break}}return!1}function Ni(e,r){let t={},n=r.printWidth,a=Si(r.endOfLine),i=0,u=[{ind:Ri(),mode:V,doc:e}],o=[],s=!1,l=[],c=0;for(Jn(e);u.length>0;){let{ind:p,mode:d,doc:D}=u.pop();switch(G(D)){case Y:{let h=a!==`
`?R(!1,D,`
`,a):D;o.push(h),u.length>0&&(i+=fr(h));break}case H:for(let h=D.length-1;h>=0;h--)u.push({ind:p,mode:d,doc:D[h]});break;case Ce:if(c>=2)throw new Error("There are too many 'cursor' in doc.");o.push(Ve),c++;break;case re:u.push({ind:lf(p,r),mode:d,doc:D.contents});break;case te:u.push({ind:ff(p,D.n,r),mode:d,doc:D.contents});break;case fe:i-=wt(o);break;case X:switch(d){case ue:if(!s){u.push({ind:p,mode:D.break?V:ue,doc:D.contents});break}case V:{s=!1;let h={ind:p,mode:ue,doc:D.contents},m=n-i,F=l.length>0;if(!D.break&&Ur(h,u,m,F,t))u.push(h);else if(D.expandedStates){let A=z(!1,D.expandedStates,-1);if(D.break){u.push({ind:p,mode:V,doc:A});break}else for(let v=1;v<D.expandedStates.length+1;v++)if(v>=D.expandedStates.length){u.push({ind:p,mode:V,doc:A});break}else{let B=D.expandedStates[v],b={ind:p,mode:ue,doc:B};if(Ur(b,u,m,F,t)){u.push(b);break}}}else u.push({ind:p,mode:V,doc:D.contents});break}}D.id&&(t[D.id]=z(!1,u,-1).mode);break;case J:{let h=n-i,m=D[At]??0,{parts:F}=D,A=F.length-m;if(A===0)break;let v=F[m+0],B=F[m+1],b={ind:p,mode:ue,doc:v},g={ind:p,mode:V,doc:v},y=Ur(b,[],h,l.length>0,t,!0);if(A===1){y?u.push(b):u.push(g);break}let w={ind:p,mode:ue,doc:B},E={ind:p,mode:V,doc:B};if(A===2){y?u.push(w,b):u.push(E,g);break}let x=F[m+2],k={ind:p,mode:d,doc:{...D,[At]:m+2}};Ur({ind:p,mode:ue,doc:[v,B,x]},[],h,l.length>0,t,!0)?u.push(k,w,b):y?u.push(k,E,b):u.push(k,E,g);break}case K:case De:{let h=D.groupId?t[D.groupId]:d;if(h===V){let m=D.type===K?D.breakContents:D.negate?D.contents:ir(D.contents);m&&u.push({ind:p,mode:d,doc:m})}if(h===ue){let m=D.type===K?D.flatContents:D.negate?ir(D.contents):D.contents;m&&u.push({ind:p,mode:d,doc:m})}break}case pe:l.push({ind:p,mode:d,doc:D.contents});break;case he:l.length>0&&u.push({ind:p,mode:d,doc:ar});break;case $:switch(d){case ue:if(D.hard)s=!0;else{D.soft||(o.push(" "),i+=1);break}case V:if(l.length>0){u.push({ind:p,mode:d,doc:D},...l.reverse()),l.length=0;break}D.literal?p.root?(o.push(a,p.root.value),i=p.root.length):(o.push(a),i=0):(i-=wt(o),o.push(a+p.value),i=p.length);break}break;case de:u.push({ind:p,mode:d,doc:D.contents});break;case ne:break;default:throw new Te(D)}u.length===0&&l.length>0&&(u.push(...l.reverse()),l.length=0)}let f=o.indexOf(Ve);if(f!==-1){let p=o.indexOf(Ve,f+1);if(p===-1)return{formatted:o.filter(m=>m!==Ve).join("")};let d=o.slice(0,f).join(""),D=o.slice(f+1,p).join(""),h=o.slice(p+1).join("");return{formatted:d+D+h,cursorNodeStart:d.length,cursorNodeText:D}}return{formatted:o.join("")}}function Mi(e,r,t){let{node:n}=e,a=[],i=e.map(()=>e.map(({index:f})=>{let p=Ni(t(),r).formatted,d=fr(p);return a[f]=Math.max(a[f]??3,d),{text:p,width:d}},"children"),"children"),u=s(!1);if(r.proseWrap!=="never")return[ur,u];let o=s(!0);return[ur,Ue(Zn(o,u))];function s(f){return qr(ar,[c(i[0],f),l(f),...i.slice(1).map(p=>c(p,f))].map(p=>`| ${p.join(" | ")} |`))}function l(f){return a.map((p,d)=>{let D=n.align[d],h=D==="center"||D==="left"?":":"-",m=D==="center"||D==="right"?":":"-",F=f?"-":"-".repeat(p-2);return`${h}${F}${m}`})}function c(f,p){return f.map(({text:d,width:D},h)=>{if(p)return d;let m=a[h]-D,F=n.align[h],A=0;F==="right"?A=m:F==="center"&&(A=Math.floor(m/2));let v=m-A;return`${" ".repeat(A)}${d}${" ".repeat(v)}`})}}function Ui(e,r,t){let n=e.map(t,"children");return Df(n)}function Df(e){let r=[""];return function t(n){for(let a of n){let i=G(a);if(i===H){t(a);continue}let u=a,o=[];i===J&&([u,...o]=a.parts),r.push([r.pop(),u],...o)}}(e),ze(r)}var Q,xt=class{constructor(r){jn(this,Q);Wn(this,Q,new Set(r))}getLeadingWhitespaceCount(r){let t=ce(this,Q),n=0;for(let a=0;a<r.length&&t.has(r.charAt(a));a++)n++;return n}getTrailingWhitespaceCount(r){let t=ce(this,Q),n=0;for(let a=r.length-1;a>=0&&t.has(r.charAt(a));a--)n++;return n}getLeadingWhitespace(r){let t=this.getLeadingWhitespaceCount(r);return r.slice(0,t)}getTrailingWhitespace(r){let t=this.getTrailingWhitespaceCount(r);return r.slice(r.length-t)}hasLeadingWhitespace(r){return ce(this,Q).has(r.charAt(0))}hasTrailingWhitespace(r){return ce(this,Q).has(z(!1,r,-1))}trimStart(r){let t=this.getLeadingWhitespaceCount(r);return r.slice(t)}trimEnd(r){let t=this.getTrailingWhitespaceCount(r);return r.slice(0,r.length-t)}trim(r){return this.trimEnd(this.trimStart(r))}split(r,t=!1){let n=`[${le([...ce(this,Q)].join(""))}]+`,a=new RegExp(t?`(${n})`:n,"u");return r.split(a)}hasWhitespaceCharacter(r){let t=ce(this,Q);return Array.prototype.some.call(r,n=>t.has(n))}hasNonWhitespaceCharacter(r){let t=ce(this,Q);return Array.prototype.some.call(r,n=>!t.has(n))}isWhitespaceOnly(r){let t=ce(this,Q);return Array.prototype.every.call(r,n=>t.has(n))}};Q=new WeakMap;var zi=xt;var pf=["	",`
`,"\f","\r"," "],hf=new zi(pf),kt=hf;var df=/^\\?.$/su,mf=/^\n *>[ >]*$/u;function Ff(e,r){return e=gf(e,r),e=vf(e),e=bf(e,r),e=Af(e,r),e=Cf(e),e}function gf(e,r){return ye(e,t=>{if(t.type!=="text")return t;let{value:n}=t;if(n==="*"||n==="_"||!df.test(n)||t.position.end.offset-t.position.start.offset===n.length)return t;let a=r.originalText.slice(t.position.start.offset,t.position.end.offset);return mf.test(a)?t:{...t,value:a}})}function Ef(e,r,t){return ye(e,n=>{if(!n.children)return n;let a=n.children.reduce((i,u)=>{let o=z(!1,i,-1);return o&&r(o,u)?i.splice(-1,1,t(o,u)):i.push(u),i},[]);return{...n,children:a}})}function vf(e){return Ef(e,(r,t)=>r.type==="text"&&t.type==="text",(r,t)=>({type:"text",value:r.value+t.value,position:{start:r.position.start,end:t.position.end}}))}function Cf(e){return ye(e,(r,t,[n])=>{if(r.type!=="text")return r;let{value:a}=r;return n.type==="paragraph"&&(t===0&&(a=kt.trimStart(a)),t===n.children.length-1&&(a=kt.trimEnd(a))),{type:"sentence",position:r.position,children:Nr(a)}})}function bf(e,r){return ye(e,(t,n,a)=>{if(t.type==="code"){let i=/^\n?(?: {4,}|\t)/u.test(r.originalText.slice(t.position.start.offset,t.position.end.offset));if(t.isIndented=i,i)for(let u=0;u<a.length;u++){let o=a[u];if(o.hasIndentedCodeblock)break;o.type==="list"&&(o.hasIndentedCodeblock=!0)}}return t})}function Af(e,r){return ye(e,(a,i,u)=>{if(a.type==="list"&&a.children.length>0){for(let o=0;o<u.length;o++){let s=u[o];if(s.type==="list"&&!s.isAligned)return a.isAligned=!1,a}a.isAligned=n(a)}return a});function t(a){return a.children.length===0?-1:a.children[0].position.start.column-1}function n(a){if(!a.ordered)return!0;let[i,u]=a.children;if(Ye(i,r).leadingSpaces.length>1)return!0;let s=t(i);if(s===-1)return!1;if(a.children.length===1)return s%r.tabWidth===0;let l=t(u);return s!==l?!1:s%r.tabWidth===0?!0:Ye(u,r).leadingSpaces.length>1}}var Gi=Ff;function Yi(e,r){let t=[""];return e.each(()=>{let{node:n}=e,a=r();switch(n.type){case"whitespace":if(G(a)!==Y){t.push(a,"");break}default:t.push([t.pop(),a])}},"children"),ze(t)}var yf=new Set(["heading","tableCell","link","wikiLink"]),$i=new Set("!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~");function wf({parent:e}){if(e.usesCJSpaces===void 0){let r={" ":0,"":0},{children:t}=e;for(let n=1;n<t.length-1;++n){let a=t[n];if(a.type==="whitespace"&&(a.value===" "||a.value==="")){let i=t[n-1].kind,u=t[n+1].kind;(i===ie&&u===$e||i===$e&&u===ie)&&++r[a.value]}}e.usesCJSpaces=r[" "]>r[""]}return e.usesCJSpaces}function xf(e,r){if(r)return!0;let{previous:t,next:n}=e;if(!t||!n)return!0;let a=t.kind,i=n.kind;return Vi(a)&&Vi(i)||a===Le&&i===ie||i===Le&&a===ie?!0:a===sr||i===sr||a===ie&&i===ie?!1:$i.has(n.value[0])||$i.has(z(!1,t.value,-1))?!0:t.hasTrailingPunctuation||n.hasLeadingPunctuation?!1:wf(e)}function Vi(e){return e===$e||e===Le}function kf(e,r,t,n){if(t!=="always"||e.hasAncestor(u=>yf.has(u.type)))return!1;if(n)return r!=="";let{previous:a,next:i}=e;return!a||!i?!0:r===""?!1:a.kind===Le&&i.kind===ie||i.kind===Le&&a.kind===ie?!0:!(a.isCJ||i.isCJ)}function Bt(e,r,t,n){if(t==="preserve"&&r===`
`)return L;let a=r===" "||r===`
`&&xf(e,n);return kf(e,r,t,n)?a?_r:Sr:a?" ":""}var Bf=new Set(["listItem","definition"]);function ji(e){var a,i;let{previous:r,next:t}=e;return(r==null?void 0:r.type)==="sentence"&&((a=z(!1,r.children,-1))==null?void 0:a.type)==="word"&&!z(!1,r.children,-1).hasTrailingPunctuation||(t==null?void 0:t.type)==="sentence"&&((i=t.children[0])==null?void 0:i.type)==="word"&&!t.children[0].hasLeadingPunctuation}function Tf(e,r,t){var a;let{node:n}=e;if(Lf(e)){let i=[""],u=Nr(r.originalText.slice(n.position.start.offset,n.position.end.offset));for(let o of u){if(o.type==="word"){i.push([i.pop(),o.value]);continue}let s=Bt(e,o.value,r.proseWrap,!0);if(G(s)===Y){i.push([i.pop(),s]);continue}i.push(s,"")}return ze(i)}switch(n.type){case"front-matter":return r.originalText.slice(n.position.start.offset,n.position.end.offset);case"root":return n.children.length===0?"":[Sf(e,r,t),L];case"paragraph":return Ui(e,r,t);case"sentence":return Yi(e,t);case"word":{let i=R(!1,R(!1,n.value,"*",String.raw`\*`),new RegExp([`(^|${Se.source})(_+)`,`(_+)(${Se.source}|$)`].join("|"),"gu"),(s,l,c,f,p)=>R(!1,c?`${l}${c}`:`${f}${p}`,"_",String.raw`\_`)),u=(s,l,c)=>s.type==="sentence"&&c===0,o=(s,l,c)=>Ct(s.children[c-1]);return i!==n.value&&(e.match(void 0,u,o)||e.match(void 0,u,(s,l,c)=>s.type==="emphasis"&&c===0,o))&&(i=i.replace(/^(\\?[*_])+/u,s=>R(!1,s,"\\",""))),i}case"whitespace":{let{next:i}=e,u=i&&/^>|^(?:[*+-]|#{1,6}|\d+[).])$/u.test(i.value)?"never":r.proseWrap;return Bt(e,n.value,u)}case"emphasis":{let i;if(Ct(n.children[0]))i=r.originalText[n.position.start.offset];else{let u=ji(e),o=((a=e.parent)==null?void 0:a.type)==="strong"&&ji(e.ancestors);i=u||o||e.hasAncestor(s=>s.type==="emphasis")?"*":"_"}return[i,j(e,r,t),i]}case"strong":return["**",j(e,r,t),"**"];case"delete":return["~~",j(e,r,t),"~~"];case"inlineCode":{let i=r.proseWrap==="preserve"?n.value:R(!1,n.value,`
`," "),u=ei(i,"`"),o="`".repeat(u||1),s=i.startsWith("`")||i.endsWith("`")||/^[\n ]/u.test(i)&&/[\n ]$/u.test(i)&&/[^\n ]/u.test(i)?" ":"";return[o,s,i,s,o]}case"wikiLink":{let i="";return r.proseWrap==="preserve"?i=n.value:i=R(!1,n.value,/[\t\n]+/gu," "),["[[",i,"]]"]}case"link":switch(r.originalText[n.position.start.offset]){case"<":{let i="mailto:";return["<",n.url.startsWith(i)&&r.originalText.slice(n.position.start.offset+1,n.position.start.offset+1+i.length)!==i?n.url.slice(i.length):n.url,">"]}case"[":return["[",j(e,r,t),"](",Tt(n.url,")"),zr(n.title,r),")"];default:return r.originalText.slice(n.position.start.offset,n.position.end.offset)}case"image":return["![",n.alt||"","](",Tt(n.url,")"),zr(n.title,r),")"];case"blockquote":return["> ",Ae("> ",j(e,r,t))];case"heading":return["#".repeat(n.depth)+" ",j(e,r,t)];case"code":{if(n.isIndented){let o=" ".repeat(4);return Ae(o,[o,be(n.value,L)])}let i=r.__inJsTemplate?"~":"`",u=i.repeat(Math.max(3,Pr(n.value,i)+1));return[u,n.lang||"",n.meta?" "+n.meta:"",L,be(Mr(n,r.originalText),L),L,u]}case"html":{let{parent:i,isLast:u}=e,o=i.type==="root"&&u?n.value.trimEnd():n.value,s=/^<!--.*-->$/su.test(o);return be(o,s?L:_e(nr))}case"list":{let i=Hi(n,e.parent),u=ki(n,r);return j(e,r,t,{processor(o){let s=c(),l=o.node;if(l.children.length===2&&l.children[1].type==="html"&&l.children[0].position.start.column!==l.children[1].position.start.column)return[s,Wi(o,r,t,s)];return[s,Ae(" ".repeat(s.length),Wi(o,r,t,s))];function c(){let f=n.ordered?(o.isFirst?n.start:u?1:n.start+o.index)+(i%2===0?". ":") "):i%2===0?"- ":"* ";return(n.isAligned||n.hasIndentedCodeblock)&&n.ordered?qf(f,r):f}}})}case"thematicBreak":{let{ancestors:i}=e,u=i.findIndex(s=>s.type==="list");return u===-1?"---":Hi(i[u],i[u+1])%2===0?"***":"---"}case"linkReference":return["[",j(e,r,t),"]",n.referenceType==="full"?qt(n):n.referenceType==="collapsed"?"[]":""];case"imageReference":switch(n.referenceType){case"full":return["![",n.alt||"","]",qt(n)];default:return["![",n.alt,"]",n.referenceType==="collapsed"?"[]":""]}case"definition":{let i=r.proseWrap==="always"?_r:" ";return Ue([qt(n),":",ir([i,Tt(n.url),n.title===null?"":[i,zr(n.title,r,!1)]])])}case"footnote":return["[^",j(e,r,t),"]"];case"footnoteReference":return Qi(n);case"footnoteDefinition":{let i=n.children.length===1&&n.children[0].type==="paragraph"&&(r.proseWrap==="never"||r.proseWrap==="preserve"&&n.children[0].position.start.line===n.children[0].position.end.line);return[Qi(n),": ",i?j(e,r,t):Ue([Ae(" ".repeat(4),j(e,r,t,{processor:({isFirst:u})=>u?Ue([Sr,t()]):t()}))])]}case"table":return Mi(e,r,t);case"tableCell":return j(e,r,t);case"break":return/\s/u.test(r.originalText[n.position.start.offset])?["  ",_e(nr)]:["\\",L];case"liquidNode":return be(n.value,L);case"import":case"export":case"jsx":return n.value;case"esComment":return["{/* ",n.value," */}"];case"math":return["$$",L,n.value?[be(n.value,L),L]:"","$$"];case"inlineMath":return r.originalText.slice(Pe(n),Oe(n));case"tableRow":case"listItem":case"text":default:throw new ni(n,"Markdown")}}function Wi(e,r,t,n){let{node:a}=e,i=a.checked===null?"":a.checked?"[x] ":"[ ] ";return[i,j(e,r,t,{processor({node:u,isFirst:o}){if(o&&u.type!=="list")return Ae(" ".repeat(i.length),t());let s=" ".repeat(Rf(r.tabWidth-n.length,0,3));return[s,Ae(s,t())]}})]}function qf(e,r){let t=n();return e+" ".repeat(t>=4?0:t);function n(){let a=e.length%r.tabWidth;return a===0?0:r.tabWidth-a}}function Hi(e,r){return _f(e,r,t=>t.ordered===e.ordered)}function _f(e,r,t){let n=-1;for(let a of r.children)if(a.type===e.type&&t(a)?n++:n=-1,a===e)return n}function Sf(e,r,t){let n=[],a=null,{children:i}=e.node;for(let[u,o]of i.entries())switch(_t(o)){case"start":a===null&&(a={index:u,offset:o.position.end.offset});break;case"end":a!==null&&(n.push({start:a,end:{index:u,offset:o.position.start.offset}}),a=null);break;default:break}return j(e,r,t,{processor({index:u}){if(n.length>0){let o=n[0];if(u===o.start.index)return[Ki(i[o.start.index]),r.originalText.slice(o.start.offset,o.end.offset),Ki(i[o.end.index])];if(o.start.index<u&&u<o.end.index)return!1;if(u===o.end.index)return n.shift(),!1}return t()}})}function j(e,r,t,n={}){let{processor:a=t}=n,i=[];return e.each(()=>{let u=a(e);u!==!1&&(i.length>0&&Pf(e)&&(i.push(L),(Of(e,r)||Ji(e))&&i.push(L),Ji(e)&&i.push(L)),i.push(u))},"children"),i}function Ki(e){if(e.type==="html")return e.value;if(e.type==="paragraph"&&Array.isArray(e.children)&&e.children.length===1&&e.children[0].type==="esComment")return["{/* ",e.children[0].value," */}"]}function _t(e){let r;if(e.type==="html")r=e.value.match(/^<!--\s*prettier-ignore(?:-(start|end))?\s*-->$/u);else{let t;e.type==="esComment"?t=e:e.type==="paragraph"&&e.children.length===1&&e.children[0].type==="esComment"&&(t=e.children[0]),t&&(r=t.value.match(/^prettier-ignore(?:-(start|end))?$/u))}return r?r[1]||"next":!1}function Pf({node:e,parent:r}){let t=vt.has(e.type),n=e.type==="html"&&Rr.has(r.type);return!t&&!n}function Xi(e,r){return e.type==="listItem"&&(e.spread||r.originalText.charAt(e.position.end.offset-1)===`
`)}function Of({node:e,previous:r,parent:t},n){if(Xi(r,n)||e.type==="list"&&t.type==="listItem"&&r.type==="code")return!0;let i=r.type===e.type&&Bf.has(e.type),u=t.type==="listItem"&&(e.type==="list"||!Xi(t,n)),o=_t(r)==="next",s=e.type==="html"&&r.type==="html"&&r.position.end.line+1===e.position.start.line,l=e.type==="html"&&t.type==="listItem"&&r.type==="paragraph"&&r.position.end.line+1===e.position.start.line;return!(i||u||o||s||l)}function Ji({node:e,previous:r}){let t=r.type==="list",n=e.type==="code"&&e.isIndented;return t&&n}function Lf(e){let r=e.findAncestor(t=>t.type==="linkReference"||t.type==="imageReference");return r&&(r.type!=="linkReference"||r.referenceType!=="full")}var If=(e,r)=>{for(let t of r)e=R(!1,e,t,encodeURIComponent(t));return e};function Tt(e,r=[]){let t=[" ",...Array.isArray(r)?r:[r]];return new RegExp(t.map(n=>le(n)).join("|"),"u").test(e)?`<${If(e,"<>")}>`:e}function zr(e,r,t=!0){if(!e)return"";if(t)return" "+zr(e,r,!1);if(e=R(!1,e,/\\(?=["')])/gu,""),e.includes('"')&&e.includes("'")&&!e.includes(")"))return`(${e})`;let n=ti(e,r.singleQuote);return e=R(!1,e,"\\","\\\\"),e=R(!1,e,n,`\\${n}`),`${n}${e}${n}`}function Rf(e,r,t){return Math.max(r,Math.min(e,t))}function Nf(e){return e.index>0&&_t(e.previous)==="next"}function qt(e){return`[${(0,Zi.default)(e.label)}]`}function Qi(e){return`[^${e.label}]`}var Mf={preprocess:Gi,print:Tf,embed:Bi,massageAstNode:Di,hasPrettierIgnore:Nf,insertPragma:ci,getVisitorKeys:_i},eu=Mf;var ru=[{name:"Markdown",type:"prose",extensions:[".md",".livemd",".markdown",".mdown",".mdwn",".mkd",".mkdn",".mkdown",".ronn",".scd",".workbook"],tmScope:"text.md",aceMode:"markdown",aliases:["md","pandoc"],codemirrorMode:"gfm",codemirrorMimeType:"text/x-gfm",filenames:["contents.lr","README"],wrap:!0,parsers:["markdown"],vscodeLanguageIds:["markdown"],linguistLanguageId:222},{name:"MDX",type:"prose",extensions:[".mdx"],tmScope:"text.md",aceMode:"markdown",aliases:["md","pandoc"],codemirrorMode:"gfm",codemirrorMimeType:"text/x-gfm",filenames:[],wrap:!0,parsers:["mdx"],vscodeLanguageIds:["mdx"],linguistLanguageId:222}];var St={bracketSpacing:{category:"Common",type:"boolean",default:!0,description:"Print spaces between brackets.",oppositeDescription:"Do not print spaces between brackets."},objectWrap:{category:"Common",type:"choice",default:"preserve",description:"How to wrap object literals.",choices:[{value:"preserve",description:"Keep as multi-line, if there is a newline between the opening brace and first property."},{value:"collapse",description:"Fit to a single line when possible."}]},singleQuote:{category:"Common",type:"boolean",default:!1,description:"Use single quotes instead of double quotes."},proseWrap:{category:"Common",type:"choice",default:"preserve",description:"How to wrap prose.",choices:[{value:"always",description:"Wrap prose if it exceeds the print width."},{value:"never",description:"Do not wrap prose."},{value:"preserve",description:"Wrap prose as-is."}]},bracketSameLine:{category:"Common",type:"boolean",default:!1,description:"Put > of opening tags on the last line instead of on a new line."},singleAttributePerLine:{category:"Common",type:"boolean",default:!1,description:"Enforce single attribute per line in HTML, Vue and JSX."}};var Uf={proseWrap:St.proseWrap,singleQuote:St.singleQuote},tu=Uf;var zn={};Yn(zn,{markdown:()=>tF,mdx:()=>nF,remark:()=>tF});var gl=Me(iu(),1),El=Me(gu(),1),vl=Me(pc(),1),Cl=Me(al(),1);var Hm=/^import\s/u,Km=/^export\s/u,ol=String.raw`[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*|`,sl=/<!---->|<!---?[^>-](?:-?[^-])*-->/u,Xm=/^\{\s*\/\*(.*)\*\/\s*\}/u,Jm=`

`,cl=e=>Hm.test(e),Un=e=>Km.test(e),ll=(e,r)=>{let t=r.indexOf(Jm),n=r.slice(0,t);if(Un(n)||cl(n))return e(n)({type:Un(n)?"export":"import",value:n})},fl=(e,r)=>{let t=Xm.exec(r);if(t)return e(t[0])({type:"esComment",value:t[1].trim()})};ll.locator=e=>Un(e)||cl(e)?-1:1;fl.locator=(e,r)=>e.indexOf("{",r);var Dl=function(){let{Parser:e}=this,{blockTokenizers:r,blockMethods:t,inlineTokenizers:n,inlineMethods:a}=e.prototype;r.esSyntax=ll,n.esComment=fl,t.splice(t.indexOf("paragraph"),0,"esSyntax"),a.splice(a.indexOf("text"),0,"esComment")};var Qm=function(){let e=this.Parser.prototype;e.blockMethods=["frontMatter",...e.blockMethods],e.blockTokenizers.frontMatter=r;function r(t,n){let a=Ge(n);if(a.frontMatter)return t(a.frontMatter.raw)(a.frontMatter)}r.onlyAtStart=!0},pl=Qm;function Zm(){return e=>ye(e,(r,t,[n])=>r.type!=="html"||sl.test(r.value)||Rr.has(n.type)?r:{...r,type:"jsx"})}var hl=Zm;var eF=function(){let e=this.Parser.prototype,r=e.inlineMethods;r.splice(r.indexOf("text"),0,"liquid"),e.inlineTokenizers.liquid=t;function t(n,a){let i=a.match(/^(\{%.*?%\}|\{\{.*?\}\})/su);if(i)return n(i[0])({type:"liquidNode",value:i[0]})}t.locator=function(n,a){return n.indexOf("{",a)}},dl=eF;var rF=function(){let e="wikiLink",r=/^\[\[(?<linkContents>.+?)\]\]/su,t=this.Parser.prototype,n=t.inlineMethods;n.splice(n.indexOf("link"),0,e),t.inlineTokenizers.wikiLink=a;function a(i,u){let o=r.exec(u);if(o){let s=o.groups.linkContents.trim();return i(o[0])({type:e,value:s})}}a.locator=function(i,u){return i.indexOf("[",u)}},ml=rF;function bl({isMDX:e}){return r=>{let t=(0,Cl.default)().use(vl.default,{commonmark:!0,...e&&{blocks:[ol]}}).use(gl.default).use(pl).use(El.default).use(e?Dl:Fl).use(dl).use(e?hl:Fl).use(ml);return t.run(t.parse(r))}}function Fl(){}var Al={astFormat:"mdast",hasPragma:oi,hasIgnorePragma:si,locStart:Pe,locEnd:Oe},tF={...Al,parse:bl({isMDX:!1})},nF={...Al,parse:bl({isMDX:!0})};var iF={mdast:eu};return Tl(uF);});
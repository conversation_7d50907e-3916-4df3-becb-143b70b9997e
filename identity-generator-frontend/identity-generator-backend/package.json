{"name": "identity-generator-backend", "version": "1.0.0", "description": "Backend API for Identity Generator Pro - Virtual Identity Information Generator", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["identity", "generator", "api", "virtual", "testing"], "author": "Identity Generator Pro Team", "license": "MIT", "type": "commonjs"}
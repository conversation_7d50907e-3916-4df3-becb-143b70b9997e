# Identity Generator Pro

A modern, full-stack virtual identity information generator built with React, TypeScript, Node.js, and Express. Generate realistic virtual identities for testing, development, and educational purposes.

## 🚀 Features

### Core Functionality
- **Multi-Country Support**: Generate identities for 20+ countries with authentic local formats
- **Comprehensive Data**: Full identity profiles including personal info, contact details, addresses, and more
- **Batch Generation**: Generate multiple identities at once (up to 1,000 for enterprise users)
- **Smart Customization**: Filter by country, gender, age range, and include/exclude specific fields
- **Export Options**: Export in JSON, CSV, XML, PDF, and TXT formats

### User Experience
- **Modern UI**: Clean, responsive design built with Ant Design and Tailwind CSS
- **Real-time Generation**: Instant identity creation with smooth animations
- **History Management**: Track and manage previously generated identities
- **One-click Copy**: Copy any field to clipboard with visual feedback
- **Mobile Optimized**: Fully responsive design for all devices

### Advanced Features
- **User Authentication**: Secure login/register system with JWT tokens
- **Subscription Plans**: Free, Personal, Professional, and Enterprise tiers
- **API Access**: RESTful API for developers and integrations
- **Rate Limiting**: Smart rate limiting to prevent abuse
- **Security**: CORS protection, input validation, and secure headers

## 🛠️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Redux Toolkit** for state management
- **React Router** for navigation
- **Ant Design** for UI components
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **Axios** for API calls

### Backend
- **Node.js** with Express.js
- **TypeScript** for type safety
- **JWT** for authentication
- **Helmet** for security headers
- **CORS** for cross-origin requests
- **Express Rate Limit** for API protection
- **Morgan** for logging
- **Express Validator** for input validation

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Clone the Repository
```bash
git clone <repository-url>
cd identity-generator-pro
```

### Backend Setup
```bash
cd identity-generator-backend
npm install
cp .env.example .env  # Configure your environment variables
npm run build
npm run dev
```

### Frontend Setup
```bash
cd identity-generator-frontend
npm install
npm run dev
```

## 🚀 Quick Start

1. **Start the Backend Server**:
   ```bash
   cd identity-generator-backend
   npm run dev
   ```
   Server will run on http://localhost:3001

2. **Start the Frontend Development Server**:
   ```bash
   cd identity-generator-frontend
   npm run dev
   ```
   Application will run on http://localhost:5173

3. **Open your browser** and navigate to http://localhost:5173

## 📖 API Documentation

### Base URL
```
http://localhost:3001/api
```

### Endpoints

#### Generate Single Identity
```http
POST /identity/generate
Content-Type: application/json

{
  "country": "US",
  "gender": "random",
  "ageRange": { "min": 18, "max": 65 },
  "includeSSN": true,
  "includeCreditCard": true
}
```

#### Generate Multiple Identities
```http
POST /identity/generate-batch
Content-Type: application/json

{
  "country": "US",
  "quantity": 10,
  "gender": "female",
  "includeSSN": false,
  "includeCreditCard": true
}
```

#### Get Supported Countries
```http
GET /identity/countries
```

#### Health Check
```http
GET /health
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
PORT=3001
NODE_ENV=development
JWT_SECRET=your-super-secret-jwt-key
FRONTEND_URL=http://localhost:5173
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

#### Frontend (.env)
```env
VITE_API_BASE_URL=http://localhost:3001/api
```

## 🧪 Testing

### Frontend Tests
```bash
cd identity-generator-frontend
npm run test
```

### Backend Tests
```bash
cd identity-generator-backend
npm run test
```

## 📱 Mobile Support

The application is fully responsive and optimized for:
- 📱 Mobile phones (320px+)
- 📱 Tablets (768px+)
- 💻 Desktops (1024px+)
- 🖥️ Large screens (1440px+)

## 🔒 Security Features

- **CORS Protection**: Configured for specific origins
- **Rate Limiting**: Prevents API abuse
- **Input Validation**: Server-side validation for all inputs
- **Security Headers**: Helmet.js for security headers
- **JWT Authentication**: Secure token-based authentication
- **HTTPS Ready**: Production-ready HTTPS configuration

## 📊 Subscription Plans

| Feature | Free | Personal | Professional | Enterprise |
|---------|------|----------|--------------|------------|
| Daily Generations | 5 | 500/month | 5,000/month | Unlimited |
| Batch Generation | ❌ | ✅ (10) | ✅ (100) | ✅ (1,000) |
| API Access | ❌ | ❌ | ✅ | ✅ |
| Export Formats | Basic | All | All | All |
| Support | Community | Email | Priority | Dedicated |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This tool generates **virtual/fake identity information** for testing and development purposes only. The generated data should not be used for any illegal activities, identity theft, or fraud. Users are responsible for ensuring compliance with local laws and regulations.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/identitygenerator)
- 📖 Documentation: [docs.identitygenerator.pro](https://docs.identitygenerator.pro)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)

---

Made with ❤️ by the Identity Generator Pro Team
